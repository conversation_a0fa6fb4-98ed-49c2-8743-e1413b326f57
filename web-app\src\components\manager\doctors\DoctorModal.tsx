'use client'
import { useState, useEffect } from 'react';
import { Doctor<PERSON><PERSON>tionRequest, DoctorDetailResponse, Gender } from '@/types/doctor';
import { SpecialtyDetailResponse } from '@/types/specialty';
import { createDoctor } from '@/services/doctorService';
import { getSpecialties } from '@/services/specialtyService';
import toast from 'react-hot-toast';

interface DoctorModalProps {
    isOpen: boolean;
    doctor: DoctorDetailResponse | null;
    onClose: () => void;
    onSuccess: () => void;
}

export const DoctorModal = ({ isOpen, doctor, onClose, onSuccess }: DoctorModalProps) => {
    const [loading, setLoading] = useState(false);
    const [loadingSpecialties, setLoadingSpecialties] = useState(false);
    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);
    const [errors, setErrors] = useState<Record<string, string>>({});

    const [formData, setFormData] = useState({
        email: '',
        phone: '',
        firstName: '',
        lastName: '',
        specialtyId: '',
        licenseNumber: '',
        degree: '',
        consultationFee: '',
        gender: '',
        yearsOfExperience: '',
        bio: '',
        avatar: '',
        isAvailable: true
    });

    useEffect(() => {
        if (isOpen) {
            fetchSpecialties();
            if (doctor) {
            } else {
                setFormData({
                    email: '',
                    phone: '',
                    firstName: '',
                    lastName: '',
                    specialtyId: '',
                    licenseNumber: '',
                    degree: '',
                    consultationFee: '',
                    gender: '',
                    yearsOfExperience: '',
                    bio: '',
                    avatar: '',
                    isAvailable: true
                });
            }
            setErrors({});
        }
    }, [isOpen, doctor]);

    const fetchSpecialties = async () => {
        setLoadingSpecialties(true);
        try {
            const response = await getSpecialties(1, 100);
            if (response.code === 200 && response.result) {
                setSpecialties(response.result.items || []);
            } else {
                toast.error('Không thể tải danh sách chuyên khoa', {
                    duration: 3000,
                    style: {
                        background: '#DC2626',
                        color: '#fff',
                    }
                });
            }
        } catch (error) {
            toast.error(`Lỗi khi tải danh sách chuyên khoa: ${error instanceof Error ? error.message : 'Unknown error'}`, {
                duration: 3000,
                style: {
                    background: '#DC2626',
                    color: '#fff',
                }
            });
        } finally {
            setLoadingSpecialties(false);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }));

        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};
        if (!formData.email.trim()) {
            newErrors.email = 'Email là bắt buộc';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Email không đúng định dạng';
        }
        if (!formData.phone.trim()) {
            newErrors.phone = 'Số điện thoại là bắt buộc';
        } else if (!/^[0-9]{10,11}$/.test(formData.phone)) {
            newErrors.phone = 'Số điện thoại phải có 10-11 chữ số';
        }
        if (!formData.firstName.trim()) {
            newErrors.firstName = 'Tên là bắt buộc';
        }
        if (!formData.lastName.trim()) {
            newErrors.lastName = 'Họ là bắt buộc';
        }
        if (!formData.specialtyId.trim()) {
            newErrors.specialtyId = 'Chuyên khoa là bắt buộc';
        } else if (specialties.length === 0) {
            newErrors.specialtyId = 'Không có chuyên khoa nào để chọn';
        }
        if (!formData.licenseNumber.trim()) {
            newErrors.licenseNumber = 'Số giấy phép hành nghề là bắt buộc';
        }
        if (!formData.consultationFee.trim()) {
            newErrors.consultationFee = 'Phí khám là bắt buộc';
        } else if (isNaN(Number(formData.consultationFee)) || Number(formData.consultationFee) < 0) {
            newErrors.consultationFee = 'Phí khám phải là số dương';
        }
        if (!formData.yearsOfExperience.trim()) {
            newErrors.yearsOfExperience = 'Số năm kinh nghiệm là bắt buộc';
        } else if (isNaN(Number(formData.yearsOfExperience)) || Number(formData.yearsOfExperience) < 0) {
            newErrors.yearsOfExperience = 'Số năm kinh nghiệm phải là số dương';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        if (specialties.length === 0) {
            toast.error('Không thể tạo bác sĩ khi chưa có chuyên khoa nào!', {
                duration: 4000,
                style: {
                    background: '#DC2626',
                    color: '#fff',
                }
            });
            return;
        }

        setLoading(true);

        try {
            const request: DoctorCreationRequest = {
                Email: formData.email,
                Phone: formData.phone,
                FirstName: formData.firstName,
                LastName: formData.lastName,
                SpecialtyId: Number(formData.specialtyId),
                LicenseNumber: formData.licenseNumber,
                Degree: formData.degree || undefined,
                ConsultationFee: Number(formData.consultationFee),
                Gender: formData.gender ? Number(formData.gender) as Gender : undefined,
                YearsOfExperience: Number(formData.yearsOfExperience),
                Bio: formData.bio || undefined,
                Avatar: formData.avatar || undefined
            };

            const response = await createDoctor(request);

            if (response.code === 200 && response.result) {
                toast.success('🎉 Thêm bác sĩ thành công!', {
                    duration: 3000,
                    style: {
                        background: '#059669',
                        color: '#fff',
                    }
                });
                onSuccess();
                onClose();
            } else {
                toast.error(`❌ ${response.message || 'Thêm bác sĩ thất bại. Vui lòng thử lại!'}`, {
                    duration: 4000,
                    style: {
                        background: '#DC2626',
                        color: '#fff',
                    }
                });
            }
        } catch (error: any) {
            console.error('Error creating doctor:', error);

            let errorMessage = 'Đã xảy ra lỗi khi tạo bác sĩ!';
            if (error?.message) {
                errorMessage = `❌ ${error.message}`;
            }

            toast.error(errorMessage, {
                duration: 4000,
                style: {
                    background: '#DC2626',
                    color: '#fff',
                }
            });
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {doctor ? 'Chỉnh sửa Bác sĩ' : 'Thêm Bác sĩ mới'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Tên */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Tên <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="firstName"
                                type="text"
                                value={formData.firstName}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.firstName ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập tên"
                            />
                            {errors.firstName && <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>}
                        </div>

                        {/* Họ */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Họ <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="lastName"
                                type="text"
                                value={formData.lastName}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.lastName ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập họ"
                            />
                            {errors.lastName && <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>}
                        </div>

                        {/* Email */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Email <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.email ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập email"
                            />
                            {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                        </div>

                        {/* Số điện thoại */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Số điện thoại <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="phone"
                                type="tel"
                                value={formData.phone}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.phone ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập số điện thoại"
                            />
                            {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Chuyên khoa */}
                        <div>
                            <div className="flex items-center justify-between">
                                <label className="block text-sm font-medium text-gray-700">
                                    Chuyên khoa <span className="text-red-500">*</span>
                                </label>
                                <button
                                    type="button"
                                    onClick={fetchSpecialties}
                                    disabled={loadingSpecialties}
                                    className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 flex items-center space-x-1"
                                >
                                    <svg className={`h-4 w-4 ${loadingSpecialties ? 'animate-spin' : ''}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    <span>Tải lại</span>
                                </button>
                            </div>
                            <div className="relative">
                                <select
                                    name="specialtyId"
                                    value={formData.specialtyId}
                                    onChange={handleInputChange}
                                    disabled={loadingSpecialties}
                                    className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.specialtyId ? 'border-red-500' : 'border-gray-300'
                                        } ${loadingSpecialties ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                                >
                                    <option value="">
                                        {loadingSpecialties ? 'Đang tải chuyên khoa...' : 'Chọn chuyên khoa'}
                                    </option>
                                    {!loadingSpecialties && specialties.map((specialty) => (
                                        <option key={specialty.id} value={specialty.id}>
                                            {specialty.specialtyName}
                                        </option>
                                    ))}
                                </select>
                                {loadingSpecialties && (
                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <svg className="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </div>
                                )}
                            </div>
                            {errors.specialtyId && <p className="mt-1 text-sm text-red-600">{errors.specialtyId}</p>}
                            {!loadingSpecialties && specialties.length === 0 && (
                                <div className="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                                    <p className="text-sm text-yellow-800">
                                        ⚠️ Không có chuyên khoa nào. Vui lòng thêm chuyên khoa trước khi tạo bác sĩ.
                                    </p>
                                </div>
                            )}
                        </div>

                        {/* Số giấy phép hành nghề */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Số giấy phép hành nghề <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="licenseNumber"
                                type="text"
                                value={formData.licenseNumber}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.licenseNumber ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập số giấy phép"
                            />
                            {errors.licenseNumber && <p className="mt-1 text-sm text-red-600">{errors.licenseNumber}</p>}
                        </div>

                        {/* Bằng cấp */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Bằng cấp</label>
                            <input
                                name="degree"
                                type="text"
                                value={formData.degree}
                                onChange={handleInputChange}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Nhập bằng cấp"
                            />
                        </div>

                        {/* Phí khám */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Phí khám (VNĐ) <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="consultationFee"
                                type="number"
                                value={formData.consultationFee}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.consultationFee ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập phí khám"
                                min="0"
                            />
                            {errors.consultationFee && <p className="mt-1 text-sm text-red-600">{errors.consultationFee}</p>}
                        </div>

                        {/* Số năm kinh nghiệm */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Số năm kinh nghiệm <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="yearsOfExperience"
                                type="number"
                                value={formData.yearsOfExperience}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.yearsOfExperience ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập số năm kinh nghiệm"
                                min="0"
                            />
                            {errors.yearsOfExperience && <p className="mt-1 text-sm text-red-600">{errors.yearsOfExperience}</p>}
                        </div>

                        {/* Giới tính */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Giới tính</label>
                            <select
                                name="gender"
                                value={formData.gender}
                                onChange={handleInputChange}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Chọn giới tính</option>
                                <option value="0">Nam</option>
                                <option value="1">Nữ</option>
                                <option value="2">Khác</option>
                            </select>
                        </div>

                        {/* Trạng thái hoạt động */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                            <div className="mt-2">
                                <label className="inline-flex items-center">
                                    <input
                                        type="checkbox"
                                        name="isAvailable"
                                        checked={formData.isAvailable}
                                        onChange={handleInputChange}
                                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">Đang hoạt động</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Tiểu sử */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Tiểu sử</label>
                        <textarea
                            name="bio"
                            value={formData.bio}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Nhập tiểu sử bác sĩ"
                        />
                    </div>

                    {/* Buttons */}
                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            disabled={loading}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                        >
                            Hủy
                        </button>
                        <button
                            type="submit"
                            disabled={loading || loadingSpecialties || specialties.length === 0}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                        >
                            {loading && (
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            )}
                            <span>{loading ? 'Đang xử lý...' : (doctor ? 'Cập nhật' : 'Thêm')}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};
