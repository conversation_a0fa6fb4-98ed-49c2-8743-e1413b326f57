{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/layouts/StatsCards.tsx"], "sourcesContent": ["'use client'\r\nimport { Users, UserCheck, Calendar, Building2, TrendingUp, TrendingDown } from 'lucide-react';\r\n\r\ninterface StatsCardsProps {\r\n    stats: {\r\n        totalDoctors: number;\r\n        totalPatients: number;\r\n        totalAppointments: number;\r\n        totalDepartments: number;\r\n        appointmentsToday: number;\r\n        newPatientsThisMonth: number;\r\n    };\r\n}\r\n\r\nexport const StatsCards = ({ stats }: StatsCardsProps) => {\r\n    const cards = [\r\n        {\r\n            title: 'Tổng số Bác sĩ',\r\n            value: stats.totalDoctors,\r\n            icon: UserCheck,\r\n            color: 'bg-slate-500',\r\n            trend: '+5%',\r\n            trendUp: true\r\n        },\r\n        {\r\n            title: 'Tổng số Bệnh nhân',\r\n            value: stats.totalPatients,\r\n            icon: Users,\r\n            color: 'bg-emerald-500',\r\n            trend: '+12%',\r\n            trendUp: true\r\n        },\r\n        {\r\n            title: 'Lịch hẹn Hôm nay',\r\n            value: stats.appointmentsToday,\r\n            icon: Calendar,\r\n            color: 'bg-indigo-500',\r\n            trend: '-3%',\r\n            trendUp: false\r\n        },\r\n        {\r\n            title: 'Khoa/Phòng ban',\r\n            value: stats.totalDepartments,\r\n            icon: Building2,\r\n            color: 'bg-amber-500',\r\n            trend: '+2%',\r\n            trendUp: true\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\r\n            {cards.map((card, index) => (\r\n                <div key={index} className=\"bg-white rounded-lg shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div>\r\n                            <p className=\"text-sm font-medium text-slate-600\">{card.title}</p>\r\n                            <p className=\"text-2xl font-bold text-slate-900\">{card.value}</p>\r\n                        </div>\r\n                        <div className={`p-3 rounded-full ${card.color}`}>\r\n                            <card.icon className=\"w-6 h-6 text-white\" />\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"mt-4 flex items-center\">\r\n                        {card.trendUp ? (\r\n                            <TrendingUp className=\"w-4 h-4 text-emerald-500 mr-1\" />\r\n                        ) : (\r\n                            <TrendingDown className=\"w-4 h-4 text-rose-500 mr-1\" />\r\n                        )}\r\n                        <span className={`text-sm font-medium ${\r\n                            card.trendUp ? 'text-emerald-600' : 'text-rose-600'\r\n                        }`}>\r\n                            {card.trend}\r\n                        </span>\r\n                        <span className=\"text-sm text-slate-500 ml-1\">so với tháng trước</span>\r\n                    </div>\r\n                </div>\r\n            ))}\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AADA;;;AAcO,MAAM,aAAa,CAAC,EAAE,KAAK,EAAmB;IACjD,MAAM,QAAQ;QACV;YACI,OAAO;YACP,OAAO,MAAM,YAAY;YACzB,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,SAAS;QACb;QACA;YACI,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,SAAS;QACb;QACA;YACI,OAAO;YACP,OAAO,MAAM,iBAAiB;YAC9B,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;YACP,SAAS;QACb;QACA;YACI,OAAO;YACP,OAAO,MAAM,gBAAgB;YAC7B,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,SAAS;QACb;KACH;IAED,qBACI,6LAAC;QAAI,WAAU;kBACV,MAAM,GAAG,CAAC,CAAC,MAAM,sBACd,6LAAC;gBAAgB,WAAU;;kCACvB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;;kDACG,6LAAC;wCAAE,WAAU;kDAAsC,KAAK,KAAK;;;;;;kDAC7D,6LAAC;wCAAE,WAAU;kDAAqC,KAAK,KAAK;;;;;;;;;;;;0CAEhE,6LAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,EAAE;0CAC5C,cAAA,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG7B,6LAAC;wBAAI,WAAU;;4BACV,KAAK,OAAO,iBACT,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;qDAEtB,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CAE5B,6LAAC;gCAAK,WAAW,CAAC,oBAAoB,EAClC,KAAK,OAAO,GAAG,qBAAqB,iBACtC;0CACG,KAAK,KAAK;;;;;;0CAEf,6LAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;eArB5C;;;;;;;;;;AA2B1B;KAlEa", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/dashboard/RecentActivities.tsx"], "sourcesContent": ["'use client'\r\nimport { AlertCircle, CheckCircle } from 'lucide-react';\r\n\r\ninterface Activity {\r\n    id: number;\r\n    type: 'appointment' | 'doctor' | 'patient' | 'department';\r\n    message: string;\r\n    time: string;\r\n    status: 'pending' | 'completed';\r\n}\r\n\r\ninterface RecentActivitiesProps {\r\n    activities: Activity[];\r\n}\r\n\r\nexport const RecentActivities = ({ activities }: RecentActivitiesProps) => {\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Hoạt động gần đây</h2>\r\n            <div className=\"space-y-4\">\r\n                {activities.map((activity) => (\r\n                    <div key={activity.id} className=\"flex items-center space-x-3\">\r\n                        <div className={`p-2 rounded-full ${\r\n                            activity.status === 'completed' ? 'bg-green-100' : 'bg-yellow-100'\r\n                        }`}>\r\n                            {activity.status === 'completed' ? (\r\n                                <CheckCircle className=\"w-4 h-4 text-green-600\" />\r\n                            ) : (\r\n                                <AlertCircle className=\"w-4 h-4 text-yellow-600\" />\r\n                            )}\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                            <p className=\"text-sm text-gray-900\">{activity.message}</p>\r\n                            <p className=\"text-xs text-gray-500\">{activity.time}</p>\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AAAA;AADA;;;AAeO,MAAM,mBAAmB,CAAC,EAAE,UAAU,EAAyB;IAClE,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,6LAAC;gBAAI,WAAU;0BACV,WAAW,GAAG,CAAC,CAAC,yBACb,6LAAC;wBAAsB,WAAU;;0CAC7B,6LAAC;gCAAI,WAAW,CAAC,iBAAiB,EAC9B,SAAS,MAAM,KAAK,cAAc,iBAAiB,iBACrD;0CACG,SAAS,MAAM,KAAK,4BACjB,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAG/B,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAE,WAAU;kDAAyB,SAAS,OAAO;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDAAyB,SAAS,IAAI;;;;;;;;;;;;;uBAZjD,SAAS,EAAE;;;;;;;;;;;;;;;;AAmBzC;KAzBa", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/dashboard/UpcomingAppointments.tsx"], "sourcesContent": ["'use client'\r\nimport { Calendar } from 'lucide-react';\r\n\r\ninterface UpcomingAppointment {\r\n    id: number;\r\n    patientName: string;\r\n    doctorName: string;\r\n    department: string;\r\n    time: string;\r\n    date: string;\r\n}\r\n\r\ninterface UpcomingAppointmentsProps {\r\n    appointments: UpcomingAppointment[];\r\n}\r\n\r\nexport const UpcomingAppointments = ({ appointments }: UpcomingAppointmentsProps) => {\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Lịch hẹn sắp tới</h2>\r\n            <div className=\"space-y-4\">\r\n                {appointments.map((appointment) => (\r\n                    <div key={appointment.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\r\n                        <div className=\"flex items-center space-x-3\">\r\n                            <div className=\"p-2 bg-blue-100 rounded-full\">\r\n                                <Calendar className=\"w-4 h-4 text-blue-600\" />\r\n                            </div>\r\n                            <div>\r\n                                <p className=\"text-sm font-medium text-gray-900\">{appointment.patientName}</p>\r\n                                <p className=\"text-xs text-gray-500\">{appointment.doctorName} - {appointment.department}</p>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"text-right\">\r\n                            <p className=\"text-sm font-medium text-gray-900\">{appointment.time}</p>\r\n                            <p className=\"text-xs text-gray-500\">{appointment.date}</p>\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AADA;;;AAgBO,MAAM,uBAAuB,CAAC,EAAE,YAAY,EAA6B;IAC5E,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,6LAAC;gBAAI,WAAU;0BACV,aAAa,GAAG,CAAC,CAAC,4BACf,6LAAC;wBAAyB,WAAU;;0CAChC,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC;;0DACG,6LAAC;gDAAE,WAAU;0DAAqC,YAAY,WAAW;;;;;;0DACzE,6LAAC;gDAAE,WAAU;;oDAAyB,YAAY,UAAU;oDAAC;oDAAI,YAAY,UAAU;;;;;;;;;;;;;;;;;;;0CAG/F,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAE,WAAU;kDAAqC,YAAY,IAAI;;;;;;kDAClE,6LAAC;wCAAE,WAAU;kDAAyB,YAAY,IAAI;;;;;;;;;;;;;uBAZpD,YAAY,EAAE;;;;;;;;;;;;;;;;AAmB5C;KAzBa", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/dashboard/QuickActions.tsx"], "sourcesContent": ["'use client'\r\nimport { User<PERSON>heck, Users, Calendar, Building2 } from 'lucide-react';\r\n\r\ninterface QuickAction {\r\n    id: string;\r\n    title: string;\r\n    icon: any;\r\n    color: string;\r\n    onClick: () => void;\r\n}\r\n\r\ninterface QuickActionsProps {\r\n    actions: QuickAction[];\r\n}\r\n\r\nexport const QuickActions = ({ actions }: QuickActionsProps) => {\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Thao tác nhanh</h2>\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n                {actions.map((action) => (\r\n                    <button\r\n                        key={action.id}\r\n                        onClick={action.onClick}\r\n                        className={`p-4 ${action.color} rounded-lg hover:opacity-80 transition-opacity`}\r\n                    >\r\n                        <action.icon className=\"w-6 h-6 text-white mx-auto mb-2\" />\r\n                        <p className=\"text-sm font-medium text-white\">{action.title}</p>\r\n                    </button>\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AAAA;;AAeO,MAAM,eAAe,CAAC,EAAE,OAAO,EAAqB;IACvD,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,6LAAC;gBAAI,WAAU;0BACV,QAAQ,GAAG,CAAC,CAAC,uBACV,6LAAC;wBAEG,SAAS,OAAO,OAAO;wBACvB,WAAW,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,+CAA+C,CAAC;;0CAE/E,6LAAC,OAAO,IAAI;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAkC,OAAO,KAAK;;;;;;;uBALtD,OAAO,EAAE;;;;;;;;;;;;;;;;AAWtC;KAlBa", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/dashboard/Dashboard.tsx"], "sourcesContent": ["'use client'\r\nimport { UserCheck, Users, Calendar, Building2 } from 'lucide-react';\r\nimport { RecentActivities } from './RecentActivities';\r\nimport { UpcomingAppointments } from './UpcomingAppointments';\r\nimport { QuickActions } from './QuickActions';\r\nimport { ManagerStats } from '@/types/manager';\r\n\r\ninterface DashboardProps {\r\n    stats: ManagerStats;\r\n}\r\n\r\nexport const Dashboard = ({ stats }: DashboardProps) => {\r\n    const recentActivities = [\r\n        {\r\n            id: 1,\r\n            type: 'appointment' as const,\r\n            message: '<PERSON><PERSON><PERSON> sĩ <PERSON>uyễn Văn A có lịch hẹn mới',\r\n            time: '2 phút trước',\r\n            status: 'pending' as const\r\n        },\r\n        {\r\n            id: 2,\r\n            type: 'doctor' as const,\r\n            message: '<PERSON><PERSON><PERSON> sĩ <PERSON>rần Thị B đã được thêm vào hệ thống',\r\n            time: '15 phút trước',\r\n            status: 'completed' as const\r\n        },\r\n        {\r\n            id: 3,\r\n            type: 'patient' as const,\r\n            message: '<PERSON><PERSON><PERSON> nh<PERSON> mới đăng ký: <PERSON><PERSON>',\r\n            time: '1 giờ trước',\r\n            status: 'completed' as const\r\n        },\r\n        {\r\n            id: 4,\r\n            type: 'department' as const,\r\n            message: '<PERSON><PERSON><PERSON> nhật thông tin khoa Tim mạch',\r\n            time: '2 giờ trước',\r\n            status: 'completed' as const\r\n        }\r\n    ];\r\n\r\n    const upcomingAppointments = [\r\n        {\r\n            id: 1,\r\n            patientName: 'Nguyễn Thị D',\r\n            doctorName: 'Bác sĩ Phạm Văn E',\r\n            department: 'Khoa Nội',\r\n            time: '09:00',\r\n            date: 'Hôm nay'\r\n        },\r\n        {\r\n            id: 2,\r\n            patientName: 'Trần Văn F',\r\n            doctorName: 'Bác sĩ Lê Thị G',\r\n            department: 'Khoa Ngoại',\r\n            time: '10:30',\r\n            date: 'Hôm nay'\r\n        },\r\n        {\r\n            id: 3,\r\n            patientName: 'Phạm Thị H',\r\n            doctorName: 'Bác sĩ Nguyễn Văn I',\r\n            department: 'Khoa Nhi',\r\n            time: '14:00',\r\n            date: 'Hôm nay'\r\n        }\r\n    ];\r\n\r\n    const quickActions = [\r\n        {\r\n            id: 'add-doctor',\r\n            title: 'Thêm Bác sĩ',\r\n            icon: UserCheck,\r\n            color: 'bg-blue-600',\r\n            onClick: () => console.log('Add doctor')\r\n        },\r\n        {\r\n            id: 'manage-patients',\r\n            title: 'Quản lý Bệnh nhân',\r\n            icon: Users,\r\n            color: 'bg-green-600',\r\n            onClick: () => console.log('Manage patients')\r\n        },\r\n        {\r\n            id: 'appointments',\r\n            title: 'Lịch hẹn',\r\n            icon: Calendar,\r\n            color: 'bg-purple-600',\r\n            onClick: () => console.log('Appointments')\r\n        },\r\n        {\r\n            id: 'departments',\r\n            title: 'Khoa/Phòng ban',\r\n            icon: Building2,\r\n            color: 'bg-orange-600',\r\n            onClick: () => console.log('Departments')\r\n        }\r\n    ];\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            <RecentActivities activities={recentActivities} />\r\n            <UpcomingAppointments appointments={upcomingAppointments} />\r\n            <QuickActions actions={quickActions} />\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAJA;;;;;;AAWO,MAAM,YAAY,CAAC,EAAE,KAAK,EAAkB;IAC/C,MAAM,mBAAmB;QACrB;YACI,IAAI;YACJ,MAAM;YACN,SAAS;YACT,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,MAAM;YACN,SAAS;YACT,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,MAAM;YACN,SAAS;YACT,MAAM;YACN,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,MAAM;YACN,SAAS;YACT,MAAM;YACN,QAAQ;QACZ;KACH;IAED,MAAM,uBAAuB;QACzB;YACI,IAAI;YACJ,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,MAAM;QACV;QACA;YACI,IAAI;YACJ,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,MAAM;QACV;QACA;YACI,IAAI;YACJ,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,MAAM;QACV;KACH;IAED,MAAM,eAAe;QACjB;YACI,IAAI;YACJ,OAAO;YACP,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC/B;QACA;YACI,IAAI;YACJ,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC/B;QACA;YACI,IAAI;YACJ,OAAO;YACP,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC/B;QACA;YACI,IAAI;YACJ,OAAO;YACP,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC/B;KACH;IAED,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,iKAAA,CAAA,mBAAgB;gBAAC,YAAY;;;;;;0BAC9B,6LAAC,qKAAA,CAAA,uBAAoB;gBAAC,cAAc;;;;;;0BACpC,6LAAC,6JAAA,CAAA,eAAY;gBAAC,SAAS;;;;;;;;;;;;AAGnC;KAjGa", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorFilters.tsx"], "sourcesContent": ["'use client'\r\nimport { Search, Filter } from 'lucide-react';\r\n\r\ninterface DoctorFiltersProps {\r\n    searchTerm: string;\r\n    selectedDepartment: string;\r\n    departments: string[];\r\n    onSearchChange: (value: string) => void;\r\n    onDepartmentChange: (value: string) => void;\r\n}\r\n\r\nexport const DoctorFilters = ({\r\n    searchTerm,\r\n    selectedDepartment,\r\n    departments,\r\n    onSearchChange,\r\n    onDepartmentChange\r\n}: DoctorFiltersProps) => {\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n                <div className=\"flex-1\">\r\n                    <div className=\"relative\">\r\n                        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                        <input\r\n                            type=\"text\"\r\n                            placeholder=\"Tìm kiếm bác sĩ...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => onSearchChange(e.target.value)}\r\n                            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                    <Filter className=\"w-4 h-4 text-gray-400\" />\r\n                    <select\r\n                        value={selectedDepartment}\r\n                        onChange={(e) => onDepartmentChange(e.target.value)}\r\n                        className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    >\r\n                        {departments.map((dept) => (\r\n                            <option key={dept} value={dept === 'Tất cả' ? 'all' : dept}>\r\n                                {dept}\r\n                            </option>\r\n                        ))}\r\n                    </select>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AAAA;AADA;;;AAWO,MAAM,gBAAgB,CAAC,EAC1B,UAAU,EACV,kBAAkB,EAClB,WAAW,EACX,cAAc,EACd,kBAAkB,EACD;IACjB,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACG,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;8BAItB,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BACG,OAAO;4BACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4BAClD,WAAU;sCAET,YAAY,GAAG,CAAC,CAAC,qBACd,6LAAC;oCAAkB,OAAO,SAAS,WAAW,QAAQ;8CACjD;mCADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;KAvCa", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorTable.tsx"], "sourcesContent": ["'use client'\r\nimport { Edit, Trash2, Eye } from 'lucide-react';\r\nimport { Doctor } from '@/types/doctor';\r\n\r\ninterface DoctorTableProps {\r\n    doctors: Doctor[];\r\n    onView: (doctor: Doctor) => void;\r\n    onEdit: (doctor: Doctor) => void;\r\n    onDelete: (doctor: Doctor) => void;\r\n}\r\n\r\nexport const DoctorTable = ({ doctors, onView, onEdit, onDelete }: DoctorTableProps) => {\r\n    const getStatusColor = (status: string) => {\r\n        switch (status) {\r\n            case 'available':\r\n                return 'bg-green-100 text-green-800';\r\n            case 'busy':\r\n                return 'bg-yellow-100 text-yellow-800';\r\n            case 'off':\r\n                return 'bg-red-100 text-red-800';\r\n            default:\r\n                return 'bg-gray-100 text-gray-800';\r\n        }\r\n    };\r\n\r\n    const getStatusText = (status: string) => {\r\n        switch (status) {\r\n            case 'available':\r\n                return 'Có sẵn';\r\n            case 'busy':\r\n                return 'Bận';\r\n            case 'off':\r\n                return 'Nghỉ';\r\n            default:\r\n                return 'Không xác định';\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\r\n            <div className=\"overflow-x-auto\">\r\n                <table className=\"min-w-full divide-y divide-gray-200\">\r\n                    <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Bác sĩ\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Khoa\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Lịch làm việc\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Trạng thái\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Thao tác\r\n                            </th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {doctors.map((doctor) => (\r\n                            <tr key={doctor.id} className=\"hover:bg-gray-50\">\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <div className=\"flex items-center\">\r\n                                        <div className=\"h-10 w-10 flex-shrink-0\">\r\n                                            <img\r\n                                                className=\"h-10 w-10 rounded-full\"\r\n                                                src={doctor.avatar || '/default-avatar.png'}\r\n                                                alt={doctor.name}\r\n                                            />\r\n                                        </div>\r\n                                        <div className=\"ml-4\">\r\n                                            <div className=\"text-sm font-medium text-gray-900\">{doctor.name}</div>\r\n                                            <div className=\"text-sm text-gray-500\">ID: {doctor.id}</div>\r\n                                        </div>\r\n                                    </div>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <div className=\"text-sm text-gray-900\">{doctor.department}</div>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <div className=\"text-sm text-gray-900\">{doctor.schedule.weekdays}</div>\r\n                                    {doctor.schedule.saturday && (\r\n                                        <div className=\"text-sm text-gray-500\">T7: {doctor.schedule.saturday}</div>\r\n                                    )}\r\n                                    {doctor.schedule.sunday && (\r\n                                        <div className=\"text-sm text-gray-500\">CN: {doctor.schedule.sunday}</div>\r\n                                    )}\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(doctor.status)}`}>\r\n                                        {getStatusText(doctor.status)}\r\n                                    </span>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                    <div className=\"flex space-x-2\">\r\n                                        <button\r\n                                            onClick={() => onView(doctor)}\r\n                                            className=\"text-blue-600 hover:text-blue-900\"\r\n                                        >\r\n                                            <Eye className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => onEdit(doctor)}\r\n                                            className=\"text-green-600 hover:text-green-900\"\r\n                                        >\r\n                                            <Edit className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => onDelete(doctor)}\r\n                                            className=\"text-red-600 hover:text-red-900\"\r\n                                        >\r\n                                            <Trash2 className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        ))}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AADA;;;AAWO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAoB;IAC/E,MAAM,iBAAiB,CAAC;QACpB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAM,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCACb,cAAA,6LAAC;;8CACG,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;;;;;;;;;;;;kCAKvG,6LAAC;wBAAM,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACV,6LAAC;gCAAmB,WAAU;;kDAC1B,6LAAC;wCAAG,WAAU;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDACG,WAAU;wDACV,KAAK,OAAO,MAAM,IAAI;wDACtB,KAAK,OAAO,IAAI;;;;;;;;;;;8DAGxB,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEAAqC,OAAO,IAAI;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;;gEAAwB;gEAAK,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kDAIjE,6LAAC;wCAAG,WAAU;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDAAyB,OAAO,UAAU;;;;;;;;;;;kDAE7D,6LAAC;wCAAG,WAAU;;0DACV,6LAAC;gDAAI,WAAU;0DAAyB,OAAO,QAAQ,CAAC,QAAQ;;;;;;4CAC/D,OAAO,QAAQ,CAAC,QAAQ,kBACrB,6LAAC;gDAAI,WAAU;;oDAAwB;oDAAK,OAAO,QAAQ,CAAC,QAAQ;;;;;;;4CAEvE,OAAO,QAAQ,CAAC,MAAM,kBACnB,6LAAC;gDAAI,WAAU;;oDAAwB;oDAAK,OAAO,QAAQ,CAAC,MAAM;;;;;;;;;;;;;kDAG1E,6LAAC;wCAAG,WAAU;kDACV,cAAA,6LAAC;4CAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,OAAO,MAAM,GAAG;sDACvG,cAAc,OAAO,MAAM;;;;;;;;;;;kDAGpC,6LAAC;wCAAG,WAAU;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDACG,SAAS,IAAM,OAAO;oDACtB,WAAU;8DAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;oDACG,SAAS,IAAM,OAAO;oDACtB,WAAU;8DAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDACG,SAAS,IAAM,SAAS;oDACxB,WAAU;8DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAnDzB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA8D9C;KAlHa", "debugId": null}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/baseUrl.ts"], "sourcesContent": ["export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7166';"], "names": [], "mappings": ";;;AAAuB;AAAhB,MAAM,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/tokenStorage.ts"], "sourcesContent": ["export const tokenStorage = {\r\n    getAccessToken: () => localStorage.getItem('accessToken'),\r\n    setAccessToken: (token: string) => localStorage.setItem('accessToken', token),\r\n    clearAccessToken: () => localStorage.removeItem('accessToken'),\r\n};"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IACxB,gBAAgB,IAAM,aAAa,OAAO,CAAC;IAC3C,gBAAgB,CAAC,QAAkB,aAAa,OAAO,CAAC,eAAe;IACvE,kBAAkB,IAAM,aAAa,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/interceptor.ts"], "sourcesContent": ["import { API_URL } from './baseUrl';\r\nimport { tokenStorage } from './tokenStorage';\r\nimport { redirect } from 'next/navigation';\r\n\r\ninterface CustomRequestInit extends RequestInit {\r\n    skipAuth?: boolean;\r\n}\r\n\r\nconst PUBLIC_ENDPOINTS = [\r\n    '/api/v1/auth/login',\r\n    '/api/v1/users',\r\n    '/api/v1/auth/forgot-password',\r\n    '/api/v1/auth/reset-password',\r\n    '/api/v1/auth/verify-email',\r\n];\r\n\r\nfunction isPublicEndpoint(url: string): boolean {\r\n    return PUBLIC_ENDPOINTS.some(endpoint => {\r\n        return url.includes(endpoint) || url.endsWith(endpoint);\r\n    });\r\n}\r\n\r\nlet refreshingPromise: Promise<boolean> | null = null;\r\n\r\nasync function refreshAccessToken(): Promise<boolean> {\r\n    const accessToken = tokenStorage.getAccessToken();\r\n    if (!accessToken) return false;\r\n\r\n    try {\r\n        const response = await fetch(`${API_URL}/api/v1/auth/refresh-token`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${accessToken}`,\r\n                'Content-Type': 'application/json'\r\n            },\r\n            // credentials: 'include'\r\n        });\r\n\r\n        if (!response.ok) throw new Error('Refresh failed');\r\n\r\n        const data = await response.json();\r\n        tokenStorage.setAccessToken(data.data.accessToken);\r\n        return true;\r\n    } catch (error) {\r\n        return false;\r\n    } finally {\r\n        refreshingPromise = null;\r\n    }\r\n}\r\n\r\nexport const fetchInterceptor = async (url: string, options: CustomRequestInit = {}): Promise<Response> => {\r\n    const requestOptions: CustomRequestInit = {\r\n        ...options,\r\n        // credentials: 'include'\r\n    };\r\n\r\n    requestOptions.headers = {\r\n        'Content-Type': 'application/json',\r\n        ...requestOptions.headers,\r\n    };\r\n\r\n    const isPublic = options.skipAuth || isPublicEndpoint(url);\r\n\r\n    if (!isPublic) {\r\n        const token = tokenStorage.getAccessToken();\r\n        if (token) {\r\n            requestOptions.headers = {\r\n                ...requestOptions.headers,\r\n                Authorization: `Bearer ${token}`,\r\n            };\r\n        }\r\n    }\r\n\r\n    try {\r\n        let response = await fetch(url, requestOptions);\r\n\r\n        if (response.status === 401 && !requestOptions.skipAuth) {\r\n            if (!refreshingPromise) {\r\n                refreshingPromise = refreshAccessToken();\r\n            }\r\n            try {\r\n                await refreshingPromise;\r\n\r\n                requestOptions.headers = {\r\n                    ...requestOptions.headers,\r\n                    Authorization: `Bearer ${tokenStorage.getAccessToken()}`,\r\n                };\r\n\r\n                response = await fetch(url, requestOptions);\r\n            } catch (error) {\r\n                console.log('Token refresh failed:', error);\r\n                redirect('/login');\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        return response;\r\n\r\n    } catch (error) {\r\n        if (error instanceof Error) {\r\n            throw error;\r\n        }\r\n        throw new Error('Network error occurred');\r\n    }\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMA,MAAM,mBAAmB;IACrB;IACA;IACA;IACA;IACA;CACH;AAED,SAAS,iBAAiB,GAAW;IACjC,OAAO,iBAAiB,IAAI,CAAC,CAAA;QACzB,OAAO,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC;IAClD;AACJ;AAEA,IAAI,oBAA6C;AAEjD,eAAe;IACX,MAAM,cAAc,+HAAA,CAAA,eAAY,CAAC,cAAc;IAC/C,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,GAAG,0HAAA,CAAA,UAAO,CAAC,0BAA0B,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,aAAa;gBACxC,gBAAgB;YACpB;QAEJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,+HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW;QACjD,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,OAAO;IACX,SAAU;QACN,oBAAoB;IACxB;AACJ;AAEO,MAAM,mBAAmB,OAAO,KAAa,UAA6B,CAAC,CAAC;IAC/E,MAAM,iBAAoC;QACtC,GAAG,OAAO;IAEd;IAEA,eAAe,OAAO,GAAG;QACrB,gBAAgB;QAChB,GAAG,eAAe,OAAO;IAC7B;IAEA,MAAM,WAAW,QAAQ,QAAQ,IAAI,iBAAiB;IAEtD,IAAI,CAAC,UAAU;QACX,MAAM,QAAQ,+HAAA,CAAA,eAAY,CAAC,cAAc;QACzC,IAAI,OAAO;YACP,eAAe,OAAO,GAAG;gBACrB,GAAG,eAAe,OAAO;gBACzB,eAAe,CAAC,OAAO,EAAE,OAAO;YACpC;QACJ;IACJ;IAEA,IAAI;QACA,IAAI,WAAW,MAAM,MAAM,KAAK;QAEhC,IAAI,SAAS,MAAM,KAAK,OAAO,CAAC,eAAe,QAAQ,EAAE;YACrD,IAAI,CAAC,mBAAmB;gBACpB,oBAAoB;YACxB;YACA,IAAI;gBACA,MAAM;gBAEN,eAAe,OAAO,GAAG;oBACrB,GAAG,eAAe,OAAO;oBACzB,eAAe,CAAC,OAAO,EAAE,+HAAA,CAAA,eAAY,CAAC,cAAc,IAAI;gBAC5D;gBAEA,WAAW,MAAM,MAAM,KAAK;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD,EAAE;YACb;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACjF;QAEA,OAAO;IAEX,EAAE,OAAO,OAAO;QACZ,IAAI,iBAAiB,OAAO;YACxB,MAAM;QACV;QACA,MAAM,IAAI,MAAM;IACpB;AACJ", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/doctorService.ts"], "sourcesContent": ["import { ApiResponse } from \"@/types/apiResonse\";\r\nimport { Doctor<PERSON><PERSON><PERSON>Request, DoctorCreationResponse } from \"@/types/doctor\";\r\nimport { API_URL } from \"@/utils/baseUrl\";\r\nimport { fetchInterceptor } from \"@/utils/interceptor\";\r\n\r\nexport const createDoctor = async (data: DoctorCreationRequest): Promise<ApiResponse<DoctorCreationResponse>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/doctors`, {\r\n        method: \"POST\",\r\n        body: JSON.stringify(data)\r\n    });\r\n\r\n    const result: ApiResponse<DoctorCreationResponse> = await response.json();\r\n    return result;\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;;AAEO,MAAM,eAAe,OAAO;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,0HAAA,CAAA,UAAO,CAAC,eAAe,CAAC,EAAE;QACjE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACzB;IAEA,MAAM,SAA8C,MAAM,SAAS,IAAI;IACvE,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/specialtyService.ts"], "sourcesContent": ["import { ApiResponse } from \"@/types/apiResonse\";\r\nimport { ErrorResponse } from \"@/types/errorResponse\";\r\nimport { SpecialtyCreationRequest, SpecialtyCreationResponse, SpecialtyDetailResponse } from \"@/types/specialty\";\r\nimport { PageResponse } from \"@/types/pageResponse\";\r\nimport { API_URL } from \"@/utils/baseUrl\";\r\nimport { fetchInterceptor } from \"@/utils/interceptor\";\r\n\r\nexport const createSpecialty = async (data: SpecialtyCreationRequest): Promise<ApiResponse<SpecialtyCreationResponse>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/specialty`, {\r\n        method: \"POST\",\r\n        body: JSON.stringify(data)\r\n    });\r\n\r\n    const result: ApiResponse<SpecialtyCreationResponse> = await response.json();\r\n    return result;\r\n};\r\n\r\nexport const getSpecialties = async (page: number = 1, size: number = 10, keyword: string = \"\"): Promise<ApiResponse<PageResponse<SpecialtyDetailResponse>>> => {\r\n    let url = `${API_URL}/api/v1/specialty?page=${page}&size=${size}`;\r\n    if (keyword.trim()) {\r\n        url += `&keyword=${encodeURIComponent(keyword)}`;\r\n    }\r\n\r\n    const response = await fetchInterceptor(url, {\r\n        method: \"GET\"\r\n    });\r\n\r\n    const result: ApiResponse<PageResponse<SpecialtyDetailResponse>> = await response.json();\r\n    return result;\r\n};\r\n\r\nexport const deleteSpecialty = async (id: number): Promise<ApiResponse<object>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/specialty/${id}`, {\r\n        method: \"DELETE\"\r\n    });\r\n\r\n    const result: ApiResponse<object> = await response.json();\r\n    return result;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAIA;AACA;;;AAEO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,0HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,EAAE;QACnE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACzB;IAEA,MAAM,SAAiD,MAAM,SAAS,IAAI;IAC1E,OAAO;AACX;AAEO,MAAM,iBAAiB,OAAO,OAAe,CAAC,EAAE,OAAe,EAAE,EAAE,UAAkB,EAAE;IAC1F,IAAI,MAAM,GAAG,0HAAA,CAAA,UAAO,CAAC,uBAAuB,EAAE,KAAK,MAAM,EAAE,MAAM;IACjE,IAAI,QAAQ,IAAI,IAAI;QAChB,OAAO,CAAC,SAAS,EAAE,mBAAmB,UAAU;IACpD;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;QACzC,QAAQ;IACZ;IAEA,MAAM,SAA6D,MAAM,SAAS,IAAI;IACtF,OAAO;AACX;AAEO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,0HAAA,CAAA,UAAO,CAAC,kBAAkB,EAAE,IAAI,EAAE;QACzE,QAAQ;IACZ;IAEA,MAAM,SAA8B,MAAM,SAAS,IAAI;IACvD,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorModal.tsx"], "sourcesContent": ["'use client'\r\nimport { useState, useEffect } from 'react';\r\nimport { Doctor, DoctorCreationRequest, Gender } from '@/types/doctor';\r\nimport { SpecialtyDetailResponse } from '@/types/specialty';\r\nimport { createDoctor } from '@/services/doctorService';\r\nimport { getSpecialties } from '@/services/specialtyService';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface DoctorModalProps {\r\n    isOpen: boolean;\r\n    doctor: Doctor | null;\r\n    onClose: () => void;\r\n    onSuccess: () => void;\r\n}\r\n\r\nexport const DoctorModal = ({ isOpen, doctor, onClose, onSuccess }: DoctorModalProps) => {\r\n    const [loading, setLoading] = useState(false);\r\n    const [loadingSpecialties, setLoadingSpecialties] = useState(false);\r\n    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);\r\n    const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n    const [formData, setFormData] = useState({\r\n        email: '',\r\n        phone: '',\r\n        firstName: '',\r\n        lastName: '',\r\n        specialtyId: '',\r\n        licenseNumber: '',\r\n        degree: '',\r\n        consultationFee: '',\r\n        gender: '',\r\n        yearsOfExperience: '',\r\n        bio: '',\r\n        avatar: '',\r\n        isAvailable: true\r\n    });\r\n\r\n    useEffect(() => {\r\n        if (isOpen) {\r\n            fetchSpecialties();\r\n            if (doctor) {\r\n                // Populate form for editing (if needed)\r\n                // setFormData({ ... });\r\n            } else {\r\n                // Reset form for new doctor\r\n                setFormData({\r\n                    email: '',\r\n                    phone: '',\r\n                    firstName: '',\r\n                    lastName: '',\r\n                    specialtyId: '',\r\n                    licenseNumber: '',\r\n                    degree: '',\r\n                    consultationFee: '',\r\n                    gender: '',\r\n                    yearsOfExperience: '',\r\n                    bio: '',\r\n                    avatar: '',\r\n                    isAvailable: true\r\n                });\r\n            }\r\n            setErrors({});\r\n        }\r\n    }, [isOpen, doctor]);\r\n\r\n    const fetchSpecialties = async () => {\r\n        setLoadingSpecialties(true);\r\n        try {\r\n            const response = await getSpecialties(1, 100); // Get all specialties\r\n            if (response.code === 200 && response.result) {\r\n                setSpecialties(response.result.items || []);\r\n            } else {\r\n                toast.error('Không thể tải danh sách chuyên khoa', {\r\n                    duration: 3000,\r\n                    style: {\r\n                        background: '#DC2626',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n            }\r\n        } catch (error) {\r\n            console.error('Error fetching specialties:', error);\r\n            toast.error('Lỗi khi tải danh sách chuyên khoa', {\r\n                duration: 3000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n        } finally {\r\n            setLoadingSpecialties(false);\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        const { name, value, type } = e.target;\r\n        setFormData(prev => ({\r\n            ...prev,\r\n            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value\r\n        }));\r\n\r\n        // Clear error when user starts typing\r\n        if (errors[name]) {\r\n            setErrors(prev => ({ ...prev, [name]: '' }));\r\n        }\r\n    };\r\n\r\n    const validateForm = (): boolean => {\r\n        const newErrors: Record<string, string> = {};\r\n\r\n        // Required fields\r\n        if (!formData.email.trim()) {\r\n            newErrors.email = 'Email là bắt buộc';\r\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\r\n            newErrors.email = 'Email không đúng định dạng';\r\n        }\r\n        if (!formData.phone.trim()) {\r\n            newErrors.phone = 'Số điện thoại là bắt buộc';\r\n        } else if (!/^[0-9]{10,11}$/.test(formData.phone)) {\r\n            newErrors.phone = 'Số điện thoại phải có 10-11 chữ số';\r\n        }\r\n        if (!formData.firstName.trim()) {\r\n            newErrors.firstName = 'Tên là bắt buộc';\r\n        }\r\n        if (!formData.lastName.trim()) {\r\n            newErrors.lastName = 'Họ là bắt buộc';\r\n        }\r\n        if (!formData.specialtyId.trim()) {\r\n            newErrors.specialtyId = 'Chuyên khoa là bắt buộc';\r\n        } else if (specialties.length === 0) {\r\n            newErrors.specialtyId = 'Không có chuyên khoa nào để chọn';\r\n        }\r\n        if (!formData.licenseNumber.trim()) {\r\n            newErrors.licenseNumber = 'Số giấy phép hành nghề là bắt buộc';\r\n        }\r\n        if (!formData.consultationFee.trim()) {\r\n            newErrors.consultationFee = 'Phí khám là bắt buộc';\r\n        } else if (isNaN(Number(formData.consultationFee)) || Number(formData.consultationFee) < 0) {\r\n            newErrors.consultationFee = 'Phí khám phải là số dương';\r\n        }\r\n        if (!formData.yearsOfExperience.trim()) {\r\n            newErrors.yearsOfExperience = 'Số năm kinh nghiệm là bắt buộc';\r\n        } else if (isNaN(Number(formData.yearsOfExperience)) || Number(formData.yearsOfExperience) < 0) {\r\n            newErrors.yearsOfExperience = 'Số năm kinh nghiệm phải là số dương';\r\n        }\r\n\r\n        setErrors(newErrors);\r\n        return Object.keys(newErrors).length === 0;\r\n    };\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        if (!validateForm()) {\r\n            return;\r\n        }\r\n\r\n        // Additional check for specialties\r\n        if (specialties.length === 0) {\r\n            toast.error('Không thể tạo bác sĩ khi chưa có chuyên khoa nào!', {\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n            return;\r\n        }\r\n\r\n        setLoading(true);\r\n\r\n        try {\r\n            const request: DoctorCreationRequest = {\r\n                Email: formData.email,\r\n                Phone: formData.phone,\r\n                FirstName: formData.firstName,\r\n                LastName: formData.lastName,\r\n                SpecialtyId: Number(formData.specialtyId),\r\n                LicenseNumber: formData.licenseNumber,\r\n                Degree: formData.degree || undefined,\r\n                ConsultationFee: Number(formData.consultationFee),\r\n                Gender: formData.gender ? Number(formData.gender) as Gender : undefined,\r\n                YearsOfExperience: Number(formData.yearsOfExperience),\r\n                Bio: formData.bio || undefined,\r\n                Avatar: formData.avatar || undefined\r\n            };\r\n\r\n            const response = await createDoctor(request);\r\n\r\n            if (response.code === 200 && response.result) {\r\n                toast.success('🎉 Thêm bác sĩ thành công!', {\r\n                    duration: 3000,\r\n                    style: {\r\n                        background: '#059669',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n                onSuccess();\r\n                onClose();\r\n            } else {\r\n                toast.error(`❌ ${response.message || 'Thêm bác sĩ thất bại. Vui lòng thử lại!'}`, {\r\n                    duration: 4000,\r\n                    style: {\r\n                        background: '#DC2626',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error creating doctor:', error);\r\n\r\n            let errorMessage = 'Đã xảy ra lỗi khi tạo bác sĩ!';\r\n            if (error?.message) {\r\n                errorMessage = `❌ ${error.message}`;\r\n            }\r\n\r\n            toast.error(errorMessage, {\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    if (!isOpen) return null;\r\n\r\n    return (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n            <div className=\"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n                    {doctor ? 'Chỉnh sửa Bác sĩ' : 'Thêm Bác sĩ mới'}\r\n                </h3>\r\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        {/* Tên */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Tên <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"firstName\"\r\n                                type=\"text\"\r\n                                value={formData.firstName}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.firstName ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập tên\"\r\n                            />\r\n                            {errors.firstName && <p className=\"mt-1 text-sm text-red-600\">{errors.firstName}</p>}\r\n                        </div>\r\n\r\n                        {/* Họ */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Họ <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"lastName\"\r\n                                type=\"text\"\r\n                                value={formData.lastName}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.lastName ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập họ\"\r\n                            />\r\n                            {errors.lastName && <p className=\"mt-1 text-sm text-red-600\">{errors.lastName}</p>}\r\n                        </div>\r\n\r\n                        {/* Email */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Email <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"email\"\r\n                                type=\"email\"\r\n                                value={formData.email}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.email ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập email\"\r\n                            />\r\n                            {errors.email && <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>}\r\n                        </div>\r\n\r\n                        {/* Số điện thoại */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Số điện thoại <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"phone\"\r\n                                type=\"tel\"\r\n                                value={formData.phone}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.phone ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập số điện thoại\"\r\n                            />\r\n                            {errors.phone && <p className=\"mt-1 text-sm text-red-600\">{errors.phone}</p>}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        {/* Chuyên khoa */}\r\n                        <div>\r\n                            <div className=\"flex items-center justify-between\">\r\n                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                    Chuyên khoa <span className=\"text-red-500\">*</span>\r\n                                </label>\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={fetchSpecialties}\r\n                                    disabled={loadingSpecialties}\r\n                                    className=\"text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 flex items-center space-x-1\"\r\n                                >\r\n                                    <svg className={`h-4 w-4 ${loadingSpecialties ? 'animate-spin' : ''}`} xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                                    </svg>\r\n                                    <span>Tải lại</span>\r\n                                </button>\r\n                            </div>\r\n                            <div className=\"relative\">\r\n                                <select\r\n                                    name=\"specialtyId\"\r\n                                    value={formData.specialtyId}\r\n                                    onChange={handleInputChange}\r\n                                    disabled={loadingSpecialties}\r\n                                    className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.specialtyId ? 'border-red-500' : 'border-gray-300'\r\n                                        } ${loadingSpecialties ? 'bg-gray-100 cursor-not-allowed' : ''}`}\r\n                                >\r\n                                    <option value=\"\">\r\n                                        {loadingSpecialties ? 'Đang tải chuyên khoa...' : 'Chọn chuyên khoa'}\r\n                                    </option>\r\n                                    {!loadingSpecialties && specialties.map((specialty) => (\r\n                                        <option key={specialty.id} value={specialty.id}>\r\n                                            {specialty.specialtyName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                                {loadingSpecialties && (\r\n                                    <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\r\n                                        <svg className=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                                        </svg>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                            {errors.specialtyId && <p className=\"mt-1 text-sm text-red-600\">{errors.specialtyId}</p>}\r\n                            {!loadingSpecialties && specialties.length === 0 && (\r\n                                <div className=\"mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md\">\r\n                                    <p className=\"text-sm text-yellow-800\">\r\n                                        ⚠️ Không có chuyên khoa nào. Vui lòng thêm chuyên khoa trước khi tạo bác sĩ.\r\n                                    </p>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n\r\n                        {/* Số giấy phép hành nghề */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Số giấy phép hành nghề <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"licenseNumber\"\r\n                                type=\"text\"\r\n                                value={formData.licenseNumber}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.licenseNumber ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập số giấy phép\"\r\n                            />\r\n                            {errors.licenseNumber && <p className=\"mt-1 text-sm text-red-600\">{errors.licenseNumber}</p>}\r\n                        </div>\r\n\r\n                        {/* Bằng cấp */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">Bằng cấp</label>\r\n                            <input\r\n                                name=\"degree\"\r\n                                type=\"text\"\r\n                                value={formData.degree}\r\n                                onChange={handleInputChange}\r\n                                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                placeholder=\"Nhập bằng cấp\"\r\n                            />\r\n                        </div>\r\n\r\n                        {/* Phí khám */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Phí khám (VNĐ) <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"consultationFee\"\r\n                                type=\"number\"\r\n                                value={formData.consultationFee}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.consultationFee ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập phí khám\"\r\n                                min=\"0\"\r\n                            />\r\n                            {errors.consultationFee && <p className=\"mt-1 text-sm text-red-600\">{errors.consultationFee}</p>}\r\n                        </div>\r\n\r\n                        {/* Số năm kinh nghiệm */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Số năm kinh nghiệm <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"yearsOfExperience\"\r\n                                type=\"number\"\r\n                                value={formData.yearsOfExperience}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.yearsOfExperience ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập số năm kinh nghiệm\"\r\n                                min=\"0\"\r\n                            />\r\n                            {errors.yearsOfExperience && <p className=\"mt-1 text-sm text-red-600\">{errors.yearsOfExperience}</p>}\r\n                        </div>\r\n\r\n                        {/* Giới tính */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">Giới tính</label>\r\n                            <select\r\n                                name=\"gender\"\r\n                                value={formData.gender}\r\n                                onChange={handleInputChange}\r\n                                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                            >\r\n                                <option value=\"\">Chọn giới tính</option>\r\n                                <option value=\"0\">Nam</option>\r\n                                <option value=\"1\">Nữ</option>\r\n                                <option value=\"2\">Khác</option>\r\n                            </select>\r\n                        </div>\r\n\r\n                        {/* Trạng thái hoạt động */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">Trạng thái</label>\r\n                            <div className=\"mt-2\">\r\n                                <label className=\"inline-flex items-center\">\r\n                                    <input\r\n                                        type=\"checkbox\"\r\n                                        name=\"isAvailable\"\r\n                                        checked={formData.isAvailable}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                    />\r\n                                    <span className=\"ml-2 text-sm text-gray-700\">Đang hoạt động</span>\r\n                                </label>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Tiểu sử */}\r\n                    <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700\">Tiểu sử</label>\r\n                        <textarea\r\n                            name=\"bio\"\r\n                            value={formData.bio}\r\n                            onChange={handleInputChange}\r\n                            rows={3}\r\n                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                            placeholder=\"Nhập tiểu sử bác sĩ\"\r\n                        />\r\n                    </div>\r\n\r\n                    {/* Buttons */}\r\n                    <div className=\"flex justify-end space-x-3 pt-4\">\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={onClose}\r\n                            disabled={loading}\r\n                            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50\"\r\n                        >\r\n                            Hủy\r\n                        </button>\r\n                        <button\r\n                            type=\"submit\"\r\n                            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\"\r\n                        >\r\n                            {loading && (\r\n                                <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                                </svg>\r\n                            )}\r\n                            <span>{loading ? 'Đang xử lý...' : (doctor ? 'Cập nhật' : 'Thêm')}</span>\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AACA;;;AANA;;;;;AAeO,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAoB;;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,eAAe;QACf,QAAQ;QACR,iBAAiB;QACjB,QAAQ;QACR,mBAAmB;QACnB,KAAK;QACL,QAAQ;QACR,aAAa;IACjB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,QAAQ;gBACR;gBACA,IAAI,QAAQ;gBACR,wCAAwC;gBACxC,wBAAwB;gBAC5B,OAAO;oBACH,4BAA4B;oBAC5B,YAAY;wBACR,OAAO;wBACP,OAAO;wBACP,WAAW;wBACX,UAAU;wBACV,aAAa;wBACb,eAAe;wBACf,QAAQ;wBACR,iBAAiB;wBACjB,QAAQ;wBACR,mBAAmB;wBACnB,KAAK;wBACL,QAAQ;wBACR,aAAa;oBACjB;gBACJ;gBACA,UAAU,CAAC;YACf;QACJ;gCAAG;QAAC;QAAQ;KAAO;IAEnB,MAAM,mBAAmB;QACrB,sBAAsB;QACtB,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,GAAG,MAAM,sBAAsB;YACrE,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,eAAe,SAAS,MAAM,CAAC,KAAK,IAAI,EAAE;YAC9C,OAAO;gBACH,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,uCAAuC;oBAC/C,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,qCAAqC;gBAC7C,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ,SAAU;YACN,sBAAsB;QAC1B;IACJ;IAEA,MAAM,oBAAoB,CAAC;QACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YAC3E,CAAC;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC9C;IACJ;IAEA,MAAM,eAAe;QACjB,MAAM,YAAoC,CAAC;QAE3C,kBAAkB;QAClB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YACxB,UAAU,KAAK,GAAG;QACtB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC3D,UAAU,KAAK,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YACxB,UAAU,KAAK,GAAG;QACtB,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC5B,UAAU,SAAS,GAAG;QAC1B;QACA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC3B,UAAU,QAAQ,GAAG;QACzB;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAC9B,UAAU,WAAW,GAAG;QAC5B,OAAO,IAAI,YAAY,MAAM,KAAK,GAAG;YACjC,UAAU,WAAW,GAAG;QAC5B;QACA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAChC,UAAU,aAAa,GAAG;QAC9B;QACA,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;YAClC,UAAU,eAAe,GAAG;QAChC,OAAO,IAAI,MAAM,OAAO,SAAS,eAAe,MAAM,OAAO,SAAS,eAAe,IAAI,GAAG;YACxF,UAAU,eAAe,GAAG;QAChC;QACA,IAAI,CAAC,SAAS,iBAAiB,CAAC,IAAI,IAAI;YACpC,UAAU,iBAAiB,GAAG;QAClC,OAAO,IAAI,MAAM,OAAO,SAAS,iBAAiB,MAAM,OAAO,SAAS,iBAAiB,IAAI,GAAG;YAC5F,UAAU,iBAAiB,GAAG;QAClC;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC7C;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACjB;QACJ;QAEA,mCAAmC;QACnC,IAAI,YAAY,MAAM,KAAK,GAAG;YAC1B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,qDAAqD;gBAC7D,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;YACA;QACJ;QAEA,WAAW;QAEX,IAAI;YACA,MAAM,UAAiC;gBACnC,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,aAAa,OAAO,SAAS,WAAW;gBACxC,eAAe,SAAS,aAAa;gBACrC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,OAAO,SAAS,eAAe;gBAChD,QAAQ,SAAS,MAAM,GAAG,OAAO,SAAS,MAAM,IAAc;gBAC9D,mBAAmB,OAAO,SAAS,iBAAiB;gBACpD,KAAK,SAAS,GAAG,IAAI;gBACrB,QAAQ,SAAS,MAAM,IAAI;YAC/B;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD,EAAE;YAEpC,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,8BAA8B;oBACxC,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;gBACA;gBACA;YACJ,OAAO;gBACH,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,SAAS,OAAO,IAAI,2CAA2C,EAAE;oBAC9E,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,IAAI,eAAe;YACnB,IAAI,OAAO,SAAS;gBAChB,eAAe,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;YACvC;YAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,cAAc;gBACtB,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAG,WAAU;8BACT,SAAS,qBAAqB;;;;;;8BAEnC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACpC,6LAAC;4BAAI,WAAU;;8CAEX,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DACnD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEvC,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,SAAS,GAAG,mBAAmB,mBACnK;4CACN,aAAY;;;;;;wCAEf,OAAO,SAAS,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,SAAS;;;;;;;;;;;;8CAInF,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DACpD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,QAAQ,GAAG,mBAAmB,mBAClK;4CACN,aAAY;;;;;;wCAEf,OAAO,QAAQ,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ;;;;;;;;;;;;8CAIjF,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DACjD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEzC,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAC/J;4CACN,aAAY;;;;;;wCAEf,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAI3E,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DACzC,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjD,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAC/J;4CACN,aAAY;;;;;;wCAEf,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;sCAI/E,6LAAC;4BAAI,WAAU;;8CAEX,6LAAC;;sDACG,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAM,WAAU;;wDAA0C;sEAC3C,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE/C,6LAAC;oDACG,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,6LAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,qBAAqB,iBAAiB,IAAI;4DAAE,OAAM;4DAA6B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC7I,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEzE,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDACG,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,UAAU;oDACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,WAAW,GAAG,mBAAmB,kBACtK,CAAC,EAAE,qBAAqB,mCAAmC,IAAI;;sEAEpE,6LAAC;4DAAO,OAAM;sEACT,qBAAqB,4BAA4B;;;;;;wDAErD,CAAC,sBAAsB,YAAY,GAAG,CAAC,CAAC,0BACrC,6LAAC;gEAA0B,OAAO,UAAU,EAAE;0EACzC,UAAU,aAAa;+DADf,UAAU,EAAE;;;;;;;;;;;gDAKhC,oCACG,6LAAC;oDAAI,WAAU;8DACX,cAAA,6LAAC;wDAAI,WAAU;wDAAqC,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACvG,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;;;;;;;;;;;;wCAKlE,OAAO,WAAW,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,WAAW;;;;;;wCAClF,CAAC,sBAAsB,YAAY,MAAM,KAAK,mBAC3C,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;8CAQnD,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DAChC,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE1D,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,aAAa;4CAC7B,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,aAAa,GAAG,mBAAmB,mBACvK;4CACN,aAAY;;;;;;wCAEf,OAAO,aAAa,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,aAAa;;;;;;;;;;;;8CAI3F,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKpB,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DACxC,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAElD,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,eAAe;4CAC/B,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,eAAe,GAAG,mBAAmB,mBACzK;4CACN,aAAY;4CACZ,KAAI;;;;;;wCAEP,OAAO,eAAe,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,eAAe;;;;;;;;;;;;8CAI/F,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;;gDAA0C;8DACpC,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtD,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,iBAAiB,GAAG,mBAAmB,mBAC3K;4CACN,aAAY;4CACZ,KAAI;;;;;;wCAEP,OAAO,iBAAiB,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,iBAAiB;;;;;;;;;;;;8CAInG,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;8CAK1B,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAM,WAAU;;kEACb,6LAAC;wDACG,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,WAAW;wDAC7B,UAAU;wDACV,WAAU;;;;;;kEAEd,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,6LAAC;;8CACG,6LAAC;oCAAM,WAAU;8CAA0C;;;;;;8CAC3D,6LAAC;oCACG,MAAK;oCACL,OAAO,SAAS,GAAG;oCACnB,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCACG,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACb;;;;;;8CAGD,6LAAC;oCACG,MAAK;oCACL,WAAU;;wCAET,yBACG,6LAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DAC/G,6LAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,6LAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;sDAG3D,6LAAC;sDAAM,UAAU,kBAAmB,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtF;GAxea;KAAA", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorsManagement.tsx"], "sourcesContent": ["'use client'\r\nimport { useState } from 'react';\r\nimport { Plus } from 'lucide-react';\r\nimport { Doctor } from '@/types/doctor';\r\nimport { DoctorFilters } from './DoctorFilters';\r\nimport { DoctorTable } from './DoctorTable';\r\nimport { DoctorModal } from './DoctorModal';\r\n\r\ninterface DoctorsManagementProps {\r\n    doctors: Doctor[];\r\n}\r\n\r\nexport const DoctorsManagement = ({ doctors }: DoctorsManagementProps) => {\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [selectedDepartment, setSelectedDepartment] = useState('all');\r\n    const [showModal, setShowModal] = useState(false);\r\n    const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);\r\n\r\n    const departments = ['Tất cả', 'Khoa <PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> mạch', 'Khoa Thần kinh'];\r\n\r\n    const filteredDoctors = doctors.filter(doctor => {\r\n        const matchesSearch = doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            doctor.department.toLowerCase().includes(searchTerm.toLowerCase());\r\n        const matchesDepartment = selectedDepartment === 'all' || doctor.department === selectedDepartment;\r\n        return matchesSearch && matchesDepartment;\r\n    });\r\n\r\n    const handleView = (doctor: Doctor) => {\r\n        setSelectedDoctor(doctor);\r\n        setShowModal(true);\r\n    };\r\n\r\n    const handleEdit = (doctor: Doctor) => {\r\n        setSelectedDoctor(doctor);\r\n        setShowModal(true);\r\n    };\r\n\r\n    const handleDelete = (doctor: Doctor) => {\r\n        if (confirm('Bạn có chắc chắn muốn xóa bác sĩ này?')) {\r\n            // Handle delete\r\n            console.log('Delete doctor:', doctor.id);\r\n        }\r\n    };\r\n\r\n    const handleModalSuccess = () => {\r\n        // Refresh doctors list here if needed\r\n        console.log('Doctor created successfully');\r\n        setShowModal(false);\r\n        setSelectedDoctor(null);\r\n    };\r\n\r\n    const handleModalClose = () => {\r\n        setShowModal(false);\r\n        setSelectedDoctor(null);\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Header */}\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Quản lý Bác sĩ</h2>\r\n                <button\r\n                    onClick={() => setShowModal(true)}\r\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\r\n                >\r\n                    <Plus className=\"w-4 h-4\" />\r\n                    <span>Thêm Bác sĩ</span>\r\n                </button>\r\n            </div>\r\n\r\n            {/* Filters */}\r\n            <DoctorFilters\r\n                searchTerm={searchTerm}\r\n                selectedDepartment={selectedDepartment}\r\n                departments={departments}\r\n                onSearchChange={setSearchTerm}\r\n                onDepartmentChange={setSelectedDepartment}\r\n            />\r\n\r\n            {/* Doctors Table */}\r\n            <DoctorTable\r\n                doctors={filteredDoctors}\r\n                onView={handleView}\r\n                onEdit={handleEdit}\r\n                onDelete={handleDelete}\r\n            />\r\n\r\n            {/* Modal */}\r\n            <DoctorModal\r\n                isOpen={showModal}\r\n                doctor={selectedDoctor}\r\n                onClose={handleModalClose}\r\n                onSuccess={handleModalSuccess}\r\n            />\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;;;AANA;;;;;;AAYO,MAAM,oBAAoB,CAAC,EAAE,OAAO,EAA0B;;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,cAAc;QAAC;QAAU;QAAY;QAAc;QAAY;QAAiB;KAAiB;IAEvG,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3E,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACnE,MAAM,oBAAoB,uBAAuB,SAAS,OAAO,UAAU,KAAK;QAChF,OAAO,iBAAiB;IAC5B;IAEA,MAAM,aAAa,CAAC;QAChB,kBAAkB;QAClB,aAAa;IACjB;IAEA,MAAM,aAAa,CAAC;QAChB,kBAAkB;QAClB,aAAa;IACjB;IAEA,MAAM,eAAe,CAAC;QAClB,IAAI,QAAQ,0CAA0C;YAClD,gBAAgB;YAChB,QAAQ,GAAG,CAAC,kBAAkB,OAAO,EAAE;QAC3C;IACJ;IAEA,MAAM,qBAAqB;QACvB,sCAAsC;QACtC,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,kBAAkB;IACtB;IAEA,MAAM,mBAAmB;QACrB,aAAa;QACb,kBAAkB;IACtB;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBACG,SAAS,IAAM,aAAa;wBAC5B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKd,6LAAC,4JAAA,CAAA,gBAAa;gBACV,YAAY;gBACZ,oBAAoB;gBACpB,aAAa;gBACb,gBAAgB;gBAChB,oBAAoB;;;;;;0BAIxB,6LAAC,0JAAA,CAAA,cAAW;gBACR,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,UAAU;;;;;;0BAId,6LAAC,0JAAA,CAAA,cAAW;gBACR,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,WAAW;;;;;;;;;;;;AAI3B;GApFa;KAAA", "debugId": null}}, {"offset": {"line": 2432, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/departments/DepartmentsManagement.tsx"], "sourcesContent": ["'use client';\r\nimport { useState, useEffect } from 'react';\r\nimport { Plus, Search, Edit, Trash2, Users, Building2, Loader2 } from 'lucide-react';\r\nimport { SpecialtyCreationRequest, SpecialtyCreationResponse, SpecialtyDetailResponse } from '@/types/specialty';\r\nimport { PageResponse } from '@/types/pageResponse';\r\nimport { ApiResponse } from '@/types/apiResonse';\r\nimport { ErrorResponse } from '@/types/errorResponse';\r\nimport { createSpecialty, getSpecialties, deleteSpecialty } from '@/services/specialtyService';\r\nimport toast from 'react-hot-toast';\r\n\r\nexport const DepartmentsManagement = () => {\r\n    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [showAddModal, setShowAddModal] = useState(false);\r\n    const [selectedSpecialty, setSelectedSpecialty] = useState<SpecialtyDetailResponse | null>(null);\r\n    const [loading, setLoading] = useState(false);\r\n    const [creating, setCreating] = useState(false);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [totalPages, setTotalPages] = useState(1);\r\n    const [pageSize] = useState(10);\r\n    const [errors, setErrors] = useState<{ name?: string; description?: string }>({}); // State cho lỗi validation\r\n\r\n    const [formData, setFormData] = useState({\r\n        name: '',\r\n        description: ''\r\n    });\r\n\r\n    const fetchSpecialties = async (page: number = 1, keyword: string = '') => {\r\n        try {\r\n            setLoading(true);\r\n            const data = await getSpecialties(page, pageSize, keyword);\r\n\r\n            if (data.code === 200 && data.result) {\r\n                setSpecialties(data.result.items || []);\r\n                setCurrentPage(data.result.currentPages);\r\n                setTotalPages(data.result.totalPages || 1);\r\n            } else {\r\n                setSpecialties([]);\r\n                toast.error('Không thể tải danh sách chuyên khoa!', { duration: 3000 });\r\n            }\r\n        } catch (error) {\r\n            console.error('Error fetching specialties:', error);\r\n            setSpecialties([]);\r\n            toast.error('Đã xảy ra lỗi khi tải danh sách chuyên khoa!', { duration: 3000 });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchSpecialties();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        const delayedSearch = setTimeout(() => {\r\n            fetchSpecialties(1, searchTerm);\r\n        }, 500);\r\n        return () => clearTimeout(delayedSearch);\r\n    }, [searchTerm]);\r\n\r\n    const validateForm = () => {\r\n        const newErrors: { name?: string; description?: string } = {};\r\n        if (!formData.name.trim()) newErrors.name = 'Tên chuyên khoa không được để trống';\r\n        if (!formData.description.trim()) newErrors.description = 'Mô tả không được để trống';\r\n        setErrors(newErrors);\r\n        return Object.keys(newErrors).length === 0;\r\n    };\r\n\r\n    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n        const { name, value } = e.target;\r\n        setFormData(prev => ({ ...prev, [name]: value }));\r\n        if (value.trim()) {\r\n            setErrors(prev => ({ ...prev, [name]: undefined }));\r\n        }\r\n    };\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        if (!validateForm()) return;\r\n\r\n        try {\r\n            setCreating(true);\r\n            const request: SpecialtyCreationRequest = {\r\n                SpecialtyName: formData.name,\r\n                Description: formData.description\r\n            };\r\n\r\n            const data = await createSpecialty(request);\r\n\r\n            if (data.code === 200 && data.result) {\r\n                toast.success('🎉 Thêm chuyên khoa thành công!', {\r\n                    duration: 3000,\r\n                    style: {\r\n                        background: '#059669',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n                setFormData({ name: '', description: '' });\r\n                setShowAddModal(false);\r\n                setSelectedSpecialty(null);\r\n                fetchSpecialties(currentPage, searchTerm);\r\n            } else {\r\n                toast.error(`❌ ${data.message || 'Thêm chuyên khoa thất bại. Vui lòng thử lại!'}`, {\r\n                    duration: 4000,\r\n                    style: {\r\n                        background: '#DC2626',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error creating specialty:', error);\r\n\r\n            // Xử lý lỗi từ backend\r\n            let errorMessage = 'Đã xảy ra lỗi khi tạo chuyên khoa!';\r\n\r\n            if (error?.message) {\r\n                errorMessage = `❌ ${error.message}`;\r\n            }\r\n\r\n            toast.error(errorMessage, {\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n        } finally {\r\n            setCreating(false);\r\n        }\r\n    };\r\n\r\n    const handleEdit = (specialty: SpecialtyDetailResponse) => {\r\n        setSelectedSpecialty(specialty);\r\n        setFormData({ name: specialty.specialtyName, description: specialty.description });\r\n        setShowAddModal(true);\r\n    };\r\n\r\n    const handleCloseModal = () => {\r\n        setShowAddModal(false);\r\n        setSelectedSpecialty(null);\r\n        setFormData({ name: '', description: '' });\r\n        setErrors({});\r\n    };\r\n\r\n    const handlePageChange = (page: number) => {\r\n        setCurrentPage(page);\r\n        fetchSpecialties(page, searchTerm);\r\n    };\r\n\r\n    const DeleteById = async (id: number) => {\r\n        if (!window.confirm('Bạn có chắc chắn muốn xóa chuyên khoa này?')) return;\r\n\r\n        try {\r\n            setLoading(true);\r\n            const data = await deleteSpecialty(id);\r\n\r\n            if (data.code === 200) {\r\n                toast.success('🗑️ Xóa chuyên khoa thành công!', {\r\n                    duration: 3000,\r\n                    style: {\r\n                        background: '#059669',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n                fetchSpecialties(currentPage, searchTerm);\r\n            } else {\r\n                toast.error(`❌ ${data.message || 'Xóa chuyên khoa thất bại. Vui lòng thử lại!'}`, {\r\n                    duration: 4000,\r\n                    style: {\r\n                        background: '#DC2626',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error deleting specialty:', error);\r\n\r\n            let errorMessage = 'Đã xảy ra lỗi khi xóa chuyên khoa!';\r\n            if (error?.message) {\r\n                errorMessage = `❌ ${error.message}`;\r\n            }\r\n\r\n            toast.error(errorMessage, {\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Quản lý Chuyên khoa</h2>\r\n                <button\r\n                    onClick={() => setShowAddModal(true)}\r\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\r\n                >\r\n                    <Plus className=\"w-4 h-4\" />\r\n                    <span>Thêm Chuyên khoa</span>\r\n                </button>\r\n            </div>\r\n\r\n            <div className=\"bg-white rounded-lg shadow-sm p-4\">\r\n                <div className=\"relative\">\r\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                    <input\r\n                        type=\"text\"\r\n                        placeholder=\"Tìm kiếm chuyên khoa...\"\r\n                        value={searchTerm}\r\n                        onChange={(e) => setSearchTerm(e.target.value)}\r\n                        className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {loading && (\r\n                <div className=\"flex justify-center items-center py-8\">\r\n                    <Loader2 className=\"w-6 h-6 animate-spin text-blue-600\" />\r\n                    <span className=\"ml-2 text-gray-600\">Đang tải...</span>\r\n                </div>\r\n            )}\r\n\r\n            {!loading && specialties.length > 0 && (\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                    {specialties.map((specialty) => (\r\n                        <div key={specialty.id} className=\"bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow\">\r\n                            <div className=\"flex items-center justify-between mb-4\">\r\n                                <div className=\"flex items-center space-x-3\">\r\n                                    <div className=\"p-2 bg-blue-100 rounded-full\">\r\n                                        <Building2 className=\"w-6 h-6 text-blue-600\" />\r\n                                    </div>\r\n                                    <div>\r\n                                        <h3 className=\"text-lg font-semibold text-gray-900\">{specialty.specialtyName}</h3>\r\n                                        <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\r\n                                            Hoạt động\r\n                                        </span>\r\n                                    </div>\r\n                                </div>\r\n                                <div className=\"flex space-x-2\">\r\n                                    <button onClick={() => handleEdit(specialty)} className=\"text-green-600 hover:text-green-900\">\r\n                                        <Edit className=\"w-4 h-4\" />\r\n                                    </button>\r\n                                    <button\r\n                                        onClick={() => DeleteById(specialty.id)}\r\n                                        className=\"text-red-600 hover:text-red-900\"\r\n                                    >\r\n                                        <Trash2 className=\"w-4 h-4\" />\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                            <p className=\"text-sm text-gray-600 mb-4\">{specialty.description}</p>\r\n                            <div className=\"space-y-3\">\r\n                                <div className=\"flex items-center justify-between text-sm\">\r\n                                    <span className=\"text-gray-500\">Số bác sĩ:</span>\r\n                                    <div className=\"flex items-center space-x-1\">\r\n                                        <Users className=\"w-4 h-4 text-gray-400\" />\r\n                                        <span className=\"font-medium text-gray-900\">{specialty.doctorNumber || 0}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div className=\"flex items-center justify-between text-sm\">\r\n                                    <span className=\"text-gray-500\">Bệnh nhân:</span>\r\n                                    <span className=\"font-medium text-gray-900\">{specialty.patientNumber || 0}</span>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"mt-4 pt-4 border-t border-gray-200\">\r\n                                <button className=\"w-full text-sm text-blue-600 hover:text-blue-800 font-medium\">\r\n                                    Xem chi tiết\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            )}\r\n\r\n            {!loading && specialties.length === 0 && (\r\n                <div key=\"empty-state\" className=\"text-center py-8\">\r\n                    <Building2 className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n                    <p className=\"text-gray-600\">Không tìm thấy chuyên khoa nào</p>\r\n                </div>\r\n            )}\r\n\r\n            {totalPages > 1 && (\r\n                <div className=\"flex justify-center items-center space-x-2 mt-6\">\r\n                    <button\r\n                        onClick={() => handlePageChange(currentPage - 1)}\r\n                        disabled={currentPage === 1}\r\n                        className=\"px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                        Trước\r\n                    </button>\r\n                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (\r\n                        <button\r\n                            key={`page-${page}`}\r\n                            onClick={() => handlePageChange(page)}\r\n                            className={`px-3 py-1 text-sm border rounded ${currentPage === page\r\n                                ? 'bg-blue-600 text-white border-blue-600'\r\n                                : 'hover:bg-gray-50'\r\n                                }`}\r\n                        >\r\n                            {page}\r\n                        </button>\r\n                    ))}\r\n                    <button\r\n                        onClick={() => handlePageChange(currentPage + 1)}\r\n                        disabled={currentPage === totalPages}\r\n                        className=\"px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                    >\r\n                        Sau\r\n                    </button>\r\n                </div>\r\n            )}\r\n\r\n            {showAddModal && (\r\n                <div key=\"modal\" className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n                    <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n                            {selectedSpecialty ? 'Chỉnh sửa Chuyên khoa' : 'Thêm Chuyên khoa mới'}\r\n                        </h3>\r\n                        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Tên chuyên khoa</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    name=\"name\"\r\n                                    value={formData.name}\r\n                                    onChange={handleInputChange}\r\n                                    onBlur={validateForm}\r\n                                    required\r\n                                    className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.name ? 'border-red-500' : 'border-gray-300'}`}\r\n                                />\r\n                                {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\r\n                            </div>\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Mô tả</label>\r\n                                <textarea\r\n                                    name=\"description\"\r\n                                    value={formData.description}\r\n                                    onChange={handleInputChange}\r\n                                    onBlur={validateForm}\r\n                                    rows={3}\r\n                                    required\r\n                                    className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.description ? 'border-red-500' : 'border-gray-300'}`}\r\n                                />\r\n                                {errors.description && <p className=\"text-red-500 text-sm mt-1\">{errors.description}</p>}\r\n                            </div>\r\n                            <div className=\"flex justify-end space-x-3 pt-4\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={handleCloseModal}\r\n                                    disabled={creating}\r\n                                    className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50\"\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    disabled={creating}\r\n                                    className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2\"\r\n                                >\r\n                                    {creating && <Loader2 className=\"w-4 h-4 animate-spin\" />}\r\n                                    <span>{selectedSpecialty ? 'Cập nhật' : 'Thêm'}</span>\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n};"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;;;AARA;;;;;AAUO,MAAM,wBAAwB;;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAC3F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2C,CAAC,IAAI,2BAA2B;IAE9G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,aAAa;IACjB;IAEA,MAAM,mBAAmB,OAAO,OAAe,CAAC,EAAE,UAAkB,EAAE;QAClE,IAAI;YACA,WAAW;YACX,MAAM,OAAO,MAAM,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,UAAU;YAElD,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,EAAE;gBAClC,eAAe,KAAK,MAAM,CAAC,KAAK,IAAI,EAAE;gBACtC,eAAe,KAAK,MAAM,CAAC,YAAY;gBACvC,cAAc,KAAK,MAAM,CAAC,UAAU,IAAI;YAC5C,OAAO;gBACH,eAAe,EAAE;gBACjB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,wCAAwC;oBAAE,UAAU;gBAAK;YACzE;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,eAAe,EAAE;YACjB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,gDAAgD;gBAAE,UAAU;YAAK;QACjF,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN;QACJ;0CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACN,MAAM,gBAAgB;iEAAW;oBAC7B,iBAAiB,GAAG;gBACxB;gEAAG;YACH;mDAAO,IAAM,aAAa;;QAC9B;0CAAG;QAAC;KAAW;IAEf,MAAM,eAAe;QACjB,MAAM,YAAqD,CAAC;QAC5D,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QAC5C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC1D,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC7C;IAEA,MAAM,oBAAoB,CAAC;QACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAC/C,IAAI,MAAM,IAAI,IAAI;YACd,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACrD;IACJ;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACA,YAAY;YACZ,MAAM,UAAoC;gBACtC,eAAe,SAAS,IAAI;gBAC5B,aAAa,SAAS,WAAW;YACrC;YAEA,MAAM,OAAO,MAAM,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD,EAAE;YAEnC,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,EAAE;gBAClC,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,mCAAmC;oBAC7C,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;gBACA,YAAY;oBAAE,MAAM;oBAAI,aAAa;gBAAG;gBACxC,gBAAgB;gBAChB,qBAAqB;gBACrB,iBAAiB,aAAa;YAClC,OAAO;gBACH,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,OAAO,IAAI,gDAAgD,EAAE;oBAC/E,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,uBAAuB;YACvB,IAAI,eAAe;YAEnB,IAAI,OAAO,SAAS;gBAChB,eAAe,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;YACvC;YAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,cAAc;gBACtB,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ,SAAU;YACN,YAAY;QAChB;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,qBAAqB;QACrB,YAAY;YAAE,MAAM,UAAU,aAAa;YAAE,aAAa,UAAU,WAAW;QAAC;QAChF,gBAAgB;IACpB;IAEA,MAAM,mBAAmB;QACrB,gBAAgB;QAChB,qBAAqB;QACrB,YAAY;YAAE,MAAM;YAAI,aAAa;QAAG;QACxC,UAAU,CAAC;IACf;IAEA,MAAM,mBAAmB,CAAC;QACtB,eAAe;QACf,iBAAiB,MAAM;IAC3B;IAEA,MAAM,aAAa,OAAO;QACtB,IAAI,CAAC,OAAO,OAAO,CAAC,+CAA+C;QAEnE,IAAI;YACA,WAAW;YACX,MAAM,OAAO,MAAM,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD,EAAE;YAEnC,IAAI,KAAK,IAAI,KAAK,KAAK;gBACnB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC,mCAAmC;oBAC7C,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;gBACA,iBAAiB,aAAa;YAClC,OAAO;gBACH,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,OAAO,IAAI,+CAA+C,EAAE;oBAC9E,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,IAAI,eAAe;YACnB,IAAI,OAAO,SAAS;gBAChB,eAAe,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;YACvC;YAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,cAAc;gBACtB,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IACA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBACG,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BACG,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;;;;;;;;;;;;;;;;YAKrB,yBACG,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAqB;;;;;;;;;;;;YAI5C,CAAC,WAAW,YAAY,MAAM,GAAG,mBAC9B,6LAAC;gBAAI,WAAU;0BACV,YAAY,GAAG,CAAC,CAAC,0BACd,6LAAC;wBAAuB,WAAU;;0CAC9B,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;0DACX,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;;kEACG,6LAAC;wDAAG,WAAU;kEAAuC,UAAU,aAAa;;;;;;kEAC5E,6LAAC;wDAAK,WAAU;kEAAuF;;;;;;;;;;;;;;;;;;kDAK/G,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAO,SAAS,IAAM,WAAW;gDAAY,WAAU;0DACpD,cAAA,6LAAC,8MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDACG,SAAS,IAAM,WAAW,UAAU,EAAE;gDACtC,WAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAI9B,6LAAC;gCAAE,WAAU;0CAA8B,UAAU,WAAW;;;;;;0CAChE,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAI,WAAU;;kEACX,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAA6B,UAAU,YAAY,IAAI;;;;;;;;;;;;;;;;;;kDAG/E,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAA6B,UAAU,aAAa,IAAI;;;;;;;;;;;;;;;;;;0CAGhF,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAO,WAAU;8CAA+D;;;;;;;;;;;;uBAxC/E,UAAU,EAAE;;;;;;;;;;YAiDjC,CAAC,WAAW,YAAY,MAAM,KAAK,mBAChC,6LAAC;gBAAsB,WAAU;;kCAC7B,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;eAFxB;;;;;YAMZ,aAAa,mBACV,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBACG,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,gBAAgB;wBAC1B,WAAU;kCACb;;;;;;oBAGA,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAA,qBACrD,6LAAC;4BAEG,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iCAAiC,EAAE,gBAAgB,OACzD,2CACA,oBACA;sCAEL;2BAPI,CAAC,KAAK,EAAE,MAAM;;;;;kCAU3B,6LAAC;wBACG,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,gBAAgB;wBAC1B,WAAU;kCACb;;;;;;;;;;;;YAMR,8BACG,6LAAC;gBAAgB,WAAU;0BACvB,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCACT,oBAAoB,0BAA0B;;;;;;sCAEnD,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACpC,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,QAAQ;4CACR,QAAQ;4CACR,WAAW,CAAC,qHAAqH,EAAE,OAAO,IAAI,GAAG,mBAAmB,mBAAmB;;;;;;wCAE1L,OAAO,IAAI,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI;;;;;;;;;;;;8CAEzE,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,OAAO,SAAS,WAAW;4CAC3B,UAAU;4CACV,QAAQ;4CACR,MAAM;4CACN,QAAQ;4CACR,WAAW,CAAC,qHAAqH,EAAE,OAAO,WAAW,GAAG,mBAAmB,mBAAmB;;;;;;wCAEjM,OAAO,WAAW,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,WAAW;;;;;;;;;;;;8CAEvF,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CACG,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDACb;;;;;;sDAGD,6LAAC;4CACG,MAAK;4CACL,UAAU;4CACV,WAAU;;gDAET,0BAAY,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DAChC,6LAAC;8DAAM,oBAAoB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eA/CnD;;;;;;;;;;;AAwDzB;GA5Wa;KAAA", "debugId": null}}, {"offset": {"line": 3193, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/appointments/AppointmentsManagement.tsx"], "sourcesContent": ["'use client'\r\nimport { useState } from 'react';\r\nimport { Search, Filter, Calendar, Clock, User, Phone, Mail } from 'lucide-react';\r\nimport { Appointment } from '@/types/appointment';\r\n\r\ninterface AppointmentsManagementProps {\r\n    appointments: Appointment[];\r\n}\r\n\r\nexport const AppointmentsManagement = ({ appointments }: AppointmentsManagementProps) => {\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [statusFilter, setStatusFilter] = useState('all');\r\n    const [dateFilter, setDateFilter] = useState('all');\r\n\r\n    const statusOptions = [\r\n        { value: 'all', label: 'Tất cả trạng thái' },\r\n        { value: 'confirmed', label: 'Đã xác nhận' },\r\n        { value: 'pending', label: 'Chờ xác nhận' },\r\n        { value: 'completed', label: 'Hoàn thành' },\r\n        { value: 'cancelled', label: 'Đã hủy' }\r\n    ];\r\n\r\n    const dateOptions = [\r\n        { value: 'all', label: 'T<PERSON>t cả ngày' },\r\n        { value: 'today', label: 'Hôm nay' },\r\n        { value: 'tomorrow', label: '<PERSON><PERSON>y mai' },\r\n        { value: 'week', label: '<PERSON>ần này' }\r\n    ];\r\n\r\n    const filteredAppointments = appointments.filter(appointment => {\r\n        const matchesSearch = \r\n            appointment.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            appointment.doctorName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            appointment.department.toLowerCase().includes(searchTerm.toLowerCase());\r\n        \r\n        const matchesStatus = statusFilter === 'all' || appointment.status === statusFilter;\r\n        \r\n        const matchesDate = (() => {\r\n            if (dateFilter === 'all') return true;\r\n            const appointmentDate = new Date(appointment.date);\r\n            const today = new Date();\r\n            \r\n            switch (dateFilter) {\r\n                case 'today':\r\n                    return appointmentDate.toDateString() === today.toDateString();\r\n                case 'tomorrow':\r\n                    const tomorrow = new Date(today);\r\n                    tomorrow.setDate(tomorrow.getDate() + 1);\r\n                    return appointmentDate.toDateString() === tomorrow.toDateString();\r\n                case 'week':\r\n                    const weekStart = new Date(today);\r\n                    weekStart.setDate(today.getDate() - today.getDay());\r\n                    const weekEnd = new Date(weekStart);\r\n                    weekEnd.setDate(weekStart.getDate() + 6);\r\n                    return appointmentDate >= weekStart && appointmentDate <= weekEnd;\r\n                default:\r\n                    return true;\r\n            }\r\n        })();\r\n\r\n        return matchesSearch && matchesStatus && matchesDate;\r\n    });\r\n\r\n    const getStatusColor = (status: string) => {\r\n        switch (status) {\r\n            case 'confirmed':\r\n                return 'bg-green-100 text-green-800';\r\n            case 'pending':\r\n                return 'bg-yellow-100 text-yellow-800';\r\n            case 'completed':\r\n                return 'bg-blue-100 text-blue-800';\r\n            case 'cancelled':\r\n                return 'bg-red-100 text-red-800';\r\n            default:\r\n                return 'bg-gray-100 text-gray-800';\r\n        }\r\n    };\r\n\r\n    const getStatusText = (status: string) => {\r\n        switch (status) {\r\n            case 'confirmed':\r\n                return 'Đã xác nhận';\r\n            case 'pending':\r\n                return 'Chờ xác nhận';\r\n            case 'completed':\r\n                return 'Hoàn thành';\r\n            case 'cancelled':\r\n                return 'Đã hủy';\r\n            default:\r\n                return 'Không xác định';\r\n        }\r\n    };\r\n\r\n    const formatDate = (dateString: string) => {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString('vi-VN');\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Header */}\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Quản lý Lịch hẹn</h2>\r\n                <div className=\"text-sm text-gray-500\">\r\n                    Tổng cộng: {filteredAppointments.length} lịch hẹn\r\n                </div>\r\n            </div>\r\n\r\n            {/* Filters */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                    <div className=\"relative\">\r\n                        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                        <input\r\n                            type=\"text\"\r\n                            placeholder=\"Tìm kiếm lịch hẹn...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        />\r\n                    </div>\r\n                    <div>\r\n                        <select\r\n                            value={statusFilter}\r\n                            onChange={(e) => setStatusFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            {statusOptions.map((option) => (\r\n                                <option key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n                    <div>\r\n                        <select\r\n                            value={dateFilter}\r\n                            onChange={(e) => setDateFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            {dateOptions.map((option) => (\r\n                                <option key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n                    <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\">\r\n                        Xuất báo cáo\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Appointments Table */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\r\n                <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                        <thead className=\"bg-gray-50\">\r\n                            <tr>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Bệnh nhân\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Bác sĩ\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Khoa\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Ngày & Giờ\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Trạng thái\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Thao tác\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {filteredAppointments.map((appointment) => (\r\n                                <tr key={appointment.id} className=\"hover:bg-gray-50\">\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center\">\r\n                                            <div className=\"h-10 w-10 flex-shrink-0\">\r\n                                                <div className=\"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center\">\r\n                                                    <User className=\"w-5 h-5 text-gray-500\" />\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"ml-4\">\r\n                                                <div className=\"text-sm font-medium text-gray-900\">{appointment.patientName}</div>\r\n                                                <div className=\"text-sm text-gray-500\">{appointment.phone}</div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-900\">{appointment.doctorName}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-900\">{appointment.department}</div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            <Calendar className=\"w-4 h-4 text-gray-400\" />\r\n                                            <span className=\"text-sm text-gray-900\">{formatDate(appointment.date)}</span>\r\n                                        </div>\r\n                                        <div className=\"flex items-center space-x-2 mt-1\">\r\n                                            <Clock className=\"w-4 h-4 text-gray-400\" />\r\n                                            <span className=\"text-sm text-gray-500\">{appointment.time}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(appointment.status)}`}>\r\n                                            {getStatusText(appointment.status)}\r\n                                        </span>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                        <div className=\"flex space-x-2\">\r\n                                            <button className=\"text-blue-600 hover:text-blue-900\">\r\n                                                Xem\r\n                                            </button>\r\n                                            <button className=\"text-green-600 hover:text-green-900\">\r\n                                                Sửa\r\n                                            </button>\r\n                                            <button className=\"text-red-600 hover:text-red-900\">\r\n                                                Hủy\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            ))}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Statistics */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-green-100 rounded-full\">\r\n                            <Calendar className=\"w-6 h-6 text-green-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Đã xác nhận</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {appointments.filter(apt => apt.status === 'confirmed').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-yellow-100 rounded-full\">\r\n                            <Clock className=\"w-6 h-6 text-yellow-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Chờ xác nhận</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {appointments.filter(apt => apt.status === 'pending').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-blue-100 rounded-full\">\r\n                            <Calendar className=\"w-6 h-6 text-blue-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Hoàn thành</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {appointments.filter(apt => apt.status === 'completed').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-red-100 rounded-full\">\r\n                            <Calendar className=\"w-6 h-6 text-red-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Đã hủy</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {appointments.filter(apt => apt.status === 'cancelled').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;;;AAFA;;;AASO,MAAM,yBAAyB,CAAC,EAAE,YAAY,EAA+B;;IAChF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB;QAClB;YAAE,OAAO;YAAO,OAAO;QAAoB;QAC3C;YAAE,OAAO;YAAa,OAAO;QAAc;QAC3C;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAa,OAAO;QAAa;QAC1C;YAAE,OAAO;YAAa,OAAO;QAAS;KACzC;IAED,MAAM,cAAc;QAChB;YAAE,OAAO;YAAO,OAAO;QAAc;QACrC;YAAE,OAAO;YAAS,OAAO;QAAU;QACnC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAQ,OAAO;QAAW;KACtC;IAED,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA;QAC7C,MAAM,gBACF,YAAY,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACrE,YAAY,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpE,YAAY,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAExE,MAAM,gBAAgB,iBAAiB,SAAS,YAAY,MAAM,KAAK;QAEvE,MAAM,cAAc,CAAC;YACjB,IAAI,eAAe,OAAO,OAAO;YACjC,MAAM,kBAAkB,IAAI,KAAK,YAAY,IAAI;YACjD,MAAM,QAAQ,IAAI;YAElB,OAAQ;gBACJ,KAAK;oBACD,OAAO,gBAAgB,YAAY,OAAO,MAAM,YAAY;gBAChE,KAAK;oBACD,MAAM,WAAW,IAAI,KAAK;oBAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;oBACtC,OAAO,gBAAgB,YAAY,OAAO,SAAS,YAAY;gBACnE,KAAK;oBACD,MAAM,YAAY,IAAI,KAAK;oBAC3B,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK,MAAM,MAAM;oBAChD,MAAM,UAAU,IAAI,KAAK;oBACzB,QAAQ,OAAO,CAAC,UAAU,OAAO,KAAK;oBACtC,OAAO,mBAAmB,aAAa,mBAAmB;gBAC9D;oBACI,OAAO;YACf;QACJ,CAAC;QAED,OAAO,iBAAiB,iBAAiB;IAC7C;IAEA,MAAM,iBAAiB,CAAC;QACpB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC;IACnC;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;4BAAwB;4BACvB,qBAAqB,MAAM;4BAAC;;;;;;;;;;;;;0BAKhD,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACG,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAGlB,6LAAC;sCACG,cAAA,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;0CAET,cAAc,GAAG,CAAC,CAAC,uBAChB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDACzC,OAAO,KAAK;uCADJ,OAAO,KAAK;;;;;;;;;;;;;;;sCAMrC,6LAAC;sCACG,cAAA,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,uBACd,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDACzC,OAAO,KAAK;uCADJ,OAAO,KAAK;;;;;;;;;;;;;;;sCAMrC,6LAAC;4BAAO,WAAU;sCAAgE;;;;;;;;;;;;;;;;;0BAO1F,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAM,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACb,cAAA,6LAAC;;sDACG,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKvG,6LAAC;gCAAM,WAAU;0CACZ,qBAAqB,GAAG,CAAC,CAAC,4BACvB,6LAAC;wCAAwB,WAAU;;0DAC/B,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGxB,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAqC,YAAY,WAAW;;;;;;8EAC3E,6LAAC;oEAAI,WAAU;8EAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAIrE,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DAAyB,YAAY,UAAU;;;;;;;;;;;0DAElE,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DAAyB,YAAY,UAAU;;;;;;;;;;;0DAElE,6LAAC;gDAAG,WAAU;;kEACV,6LAAC;wDAAI,WAAU;;0EACX,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAK,WAAU;0EAAyB,WAAW,YAAY,IAAI;;;;;;;;;;;;kEAExE,6LAAC;wDAAI,WAAU;;0EACX,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAyB,YAAY,IAAI;;;;;;;;;;;;;;;;;;0DAGjE,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,YAAY,MAAM,GAAG;8DAC5G,cAAc,YAAY,MAAM;;;;;;;;;;;0DAGzC,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAO,WAAU;sEAAoC;;;;;;sEAGtD,6LAAC;4DAAO,WAAU;sEAAsC;;;;;;sEAGxD,6LAAC;4DAAO,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;uCA3CvD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwD3C,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK9E,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK9E,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9F;GA5Ra;KAAA", "debugId": null}}, {"offset": {"line": 3994, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/patients/PatientsManagement.tsx"], "sourcesContent": ["'use client'\r\nimport { useState } from 'react';\r\nimport { Search, Filter, User, Phone, Mail, Calendar, MapPin } from 'lucide-react';\r\nimport { Patient } from '@/types/appointment';\r\n\r\ninterface PatientsManagementProps {\r\n    patients: Patient[];\r\n}\r\n\r\nexport const PatientsManagement = ({ patients }: PatientsManagementProps) => {\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [genderFilter, setGenderFilter] = useState('all');\r\n    const [ageFilter, setAgeFilter] = useState('all');\r\n\r\n    const genderOptions = [\r\n        { value: 'all', label: 'Tất cả giới tính' },\r\n        { value: 'male', label: 'Nam' },\r\n        { value: 'female', label: 'Nữ' }\r\n    ];\r\n\r\n    const ageOptions = [\r\n        { value: 'all', label: 'Tất cả độ tuổi' },\r\n        { value: 'child', label: 'Trẻ em (0-12)' },\r\n        { value: 'teen', label: '<PERSON><PERSON> thi<PERSON><PERSON> niên (13-19)' },\r\n        { value: 'adult', label: 'Ngư<PERSON><PERSON> lớn (20-59)' },\r\n        { value: 'senior', label: 'Ngư<PERSON>i cao tuổi (60+)' }\r\n    ];\r\n\r\n    const calculateAge = (birthDate: string) => {\r\n        const today = new Date();\r\n        const birth = new Date(birthDate);\r\n        let age = today.getFullYear() - birth.getFullYear();\r\n        const monthDiff = today.getMonth() - birth.getMonth();\r\n        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {\r\n            age--;\r\n        }\r\n        return age;\r\n    };\r\n\r\n    const getAgeGroup = (birthDate: string) => {\r\n        const age = calculateAge(birthDate);\r\n        if (age <= 12) return 'child';\r\n        if (age <= 19) return 'teen';\r\n        if (age <= 59) return 'adult';\r\n        return 'senior';\r\n    };\r\n\r\n    const filteredPatients = patients.filter(patient => {\r\n        const matchesSearch = \r\n            patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            patient.phone.includes(searchTerm) ||\r\n            patient.email.toLowerCase().includes(searchTerm.toLowerCase());\r\n        \r\n        const matchesGender = genderFilter === 'all' || patient.gender === genderFilter;\r\n        const matchesAge = ageFilter === 'all' || getAgeGroup(patient.birthDate) === ageFilter;\r\n\r\n        return matchesSearch && matchesGender && matchesAge;\r\n    });\r\n\r\n    const formatDate = (dateString: string) => {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString('vi-VN');\r\n    };\r\n\r\n    const getGenderText = (gender: string) => {\r\n        return gender === 'male' ? 'Nam' : 'Nữ';\r\n    };\r\n\r\n    const getGenderColor = (gender: string) => {\r\n        return gender === 'male' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800';\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Header */}\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Quản lý Bệnh nhân</h2>\r\n                <div className=\"text-sm text-gray-500\">\r\n                    Tổng cộng: {filteredPatients.length} bệnh nhân\r\n                </div>\r\n            </div>\r\n\r\n            {/* Filters */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                    <div className=\"relative\">\r\n                        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                        <input\r\n                            type=\"text\"\r\n                            placeholder=\"Tìm kiếm bệnh nhân...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        />\r\n                    </div>\r\n                    <div>\r\n                        <select\r\n                            value={genderFilter}\r\n                            onChange={(e) => setGenderFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            {genderOptions.map((option) => (\r\n                                <option key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n                    <div>\r\n                        <select\r\n                            value={ageFilter}\r\n                            onChange={(e) => setAgeFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            {ageOptions.map((option) => (\r\n                                <option key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n                    <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\">\r\n                        Xuất danh sách\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Patients Table */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\r\n                <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                        <thead className=\"bg-gray-50\">\r\n                            <tr>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Bệnh nhân\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Thông tin liên hệ\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Ngày sinh\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Giới tính\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Lần khám cuối\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Thao tác\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {filteredPatients.map((patient) => (\r\n                                <tr key={patient.id} className=\"hover:bg-gray-50\">\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"flex items-center\">\r\n                                            <div className=\"h-10 w-10 flex-shrink-0\">\r\n                                                <div className=\"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center\">\r\n                                                    <User className=\"w-5 h-5 text-gray-500\" />\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"ml-4\">\r\n                                                <div className=\"text-sm font-medium text-gray-900\">{patient.name}</div>\r\n                                                <div className=\"text-sm text-gray-500\">ID: {patient.id}</div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"space-y-1\">\r\n                                            <div className=\"flex items-center space-x-2\">\r\n                                                <Phone className=\"w-4 h-4 text-gray-400\" />\r\n                                                <span className=\"text-sm text-gray-900\">{patient.phone}</span>\r\n                                            </div>\r\n                                            <div className=\"flex items-center space-x-2\">\r\n                                                <Mail className=\"w-4 h-4 text-gray-400\" />\r\n                                                <span className=\"text-sm text-gray-500\">{patient.email}</span>\r\n                                            </div>\r\n                                            <div className=\"flex items-center space-x-2\">\r\n                                                <MapPin className=\"w-4 h-4 text-gray-400\" />\r\n                                                <span className=\"text-sm text-gray-500\">{patient.address}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-900\">{formatDate(patient.birthDate)}</div>\r\n                                        <div className=\"text-sm text-gray-500\">\r\n                                            ({calculateAge(patient.birthDate)} tuổi)\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGenderColor(patient.gender)}`}>\r\n                                            {getGenderText(patient.gender)}\r\n                                        </span>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                        <div className=\"text-sm text-gray-900\">\r\n                                            {patient.lastVisit ? formatDate(patient.lastVisit) : 'Chưa có'}\r\n                                        </div>\r\n                                    </td>\r\n                                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                        <div className=\"flex space-x-2\">\r\n                                            <button className=\"text-blue-600 hover:text-blue-900\">\r\n                                                Xem\r\n                                            </button>\r\n                                            <button className=\"text-green-600 hover:text-green-900\">\r\n                                                Sửa\r\n                                            </button>\r\n                                            <button className=\"text-purple-600 hover:text-purple-900\">\r\n                                                Lịch sử\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            ))}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Statistics */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-blue-100 rounded-full\">\r\n                            <User className=\"w-6 h-6 text-blue-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Tổng bệnh nhân</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">{patients.length}</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-green-100 rounded-full\">\r\n                            <User className=\"w-6 h-6 text-green-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Nam</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {patients.filter(p => p.gender === 'male').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-pink-100 rounded-full\">\r\n                            <User className=\"w-6 h-6 text-pink-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Nữ</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {patients.filter(p => p.gender === 'female').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-purple-100 rounded-full\">\r\n                            <Calendar className=\"w-6 h-6 text-purple-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Mới tháng này</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {patients.filter(p => {\r\n                                    if (!p.lastVisit) return false;\r\n                                    const visitDate = new Date(p.lastVisit);\r\n                                    const now = new Date();\r\n                                    return visitDate.getMonth() === now.getMonth() && \r\n                                           visitDate.getFullYear() === now.getFullYear();\r\n                                }).length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAFA;;;AASO,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB;QAClB;YAAE,OAAO;YAAO,OAAO;QAAmB;QAC1C;YAAE,OAAO;YAAQ,OAAO;QAAM;QAC9B;YAAE,OAAO;YAAU,OAAO;QAAK;KAClC;IAED,MAAM,aAAa;QACf;YAAE,OAAO;YAAO,OAAO;QAAiB;QACxC;YAAE,OAAO;YAAS,OAAO;QAAgB;QACzC;YAAE,OAAO;YAAQ,OAAO;QAA2B;QACnD;YAAE,OAAO;YAAS,OAAO;QAAoB;QAC7C;YAAE,OAAO;YAAU,OAAO;QAAuB;KACpD;IAED,MAAM,eAAe,CAAC;QAClB,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,IAAI,KAAK;QACvB,IAAI,MAAM,MAAM,WAAW,KAAK,MAAM,WAAW;QACjD,MAAM,YAAY,MAAM,QAAQ,KAAK,MAAM,QAAQ;QACnD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,IAAK;YACzE;QACJ;QACA,OAAO;IACX;IAEA,MAAM,cAAc,CAAC;QACjB,MAAM,MAAM,aAAa;QACzB,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,OAAO;IACX;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACrC,MAAM,gBACF,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,KAAK,CAAC,QAAQ,CAAC,eACvB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE/D,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QACnE,MAAM,aAAa,cAAc,SAAS,YAAY,QAAQ,SAAS,MAAM;QAE7E,OAAO,iBAAiB,iBAAiB;IAC7C;IAEA,MAAM,aAAa,CAAC;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC;IACnC;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAO,WAAW,SAAS,QAAQ;IACvC;IAEA,MAAM,iBAAiB,CAAC;QACpB,OAAO,WAAW,SAAS,8BAA8B;IAC7D;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;4BAAwB;4BACvB,iBAAiB,MAAM;4BAAC;;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACG,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAGlB,6LAAC;sCACG,cAAA,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;0CAET,cAAc,GAAG,CAAC,CAAC,uBAChB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDACzC,OAAO,KAAK;uCADJ,OAAO,KAAK;;;;;;;;;;;;;;;sCAMrC,6LAAC;sCACG,cAAA,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;0CAET,WAAW,GAAG,CAAC,CAAC,uBACb,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDACzC,OAAO,KAAK;uCADJ,OAAO,KAAK;;;;;;;;;;;;;;;sCAMrC,6LAAC;4BAAO,WAAU;sCAAgE;;;;;;;;;;;;;;;;;0BAO1F,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAM,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACb,cAAA,6LAAC;;sDACG,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKvG,6LAAC;gCAAM,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACnB,6LAAC;wCAAoB,WAAU;;0DAC3B,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGxB,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAqC,QAAQ,IAAI;;;;;;8EAChE,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0DAIlE,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;;8EACX,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAyB,QAAQ,KAAK;;;;;;;;;;;;sEAE1D,6LAAC;4DAAI,WAAU;;8EACX,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAyB,QAAQ,KAAK;;;;;;;;;;;;sEAE1D,6LAAC;4DAAI,WAAU;;8EACX,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;8EAAyB,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;0DAIpE,6LAAC;gDAAG,WAAU;;kEACV,6LAAC;wDAAI,WAAU;kEAAyB,WAAW,QAAQ,SAAS;;;;;;kEACpE,6LAAC;wDAAI,WAAU;;4DAAwB;4DACjC,aAAa,QAAQ,SAAS;4DAAE;;;;;;;;;;;;;0DAG1C,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,QAAQ,MAAM,GAAG;8DACxG,cAAc,QAAQ,MAAM;;;;;;;;;;;0DAGrC,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACV,QAAQ,SAAS,GAAG,WAAW,QAAQ,SAAS,IAAI;;;;;;;;;;;0DAG7D,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAO,WAAU;sEAAoC;;;;;;sEAGtD,6LAAC;4DAAO,WAAU;sEAAsC;;;;;;sEAGxD,6LAAC;4DAAO,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;uCAtD7D,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmEvC,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAI5E,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAKjE,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAKnE,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,SAAS,MAAM,CAAC,CAAA;gDACb,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO;gDACzB,MAAM,YAAY,IAAI,KAAK,EAAE,SAAS;gDACtC,MAAM,MAAM,IAAI;gDAChB,OAAO,UAAU,QAAQ,OAAO,IAAI,QAAQ,MACrC,UAAU,WAAW,OAAO,IAAI,WAAW;4CACtD,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAjRa;KAAA", "debugId": null}}, {"offset": {"line": 4821, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/reports/ReportsManagement.tsx"], "sourcesContent": ["'use client'\r\nimport { useState } from 'react';\r\nimport { Download, Calendar, BarChart3, TrendingUp, Users, UserCheck, Building2 } from 'lucide-react';\r\n\r\ninterface ReportsManagementProps {\r\n    stats: {\r\n        totalDoctors: number;\r\n        totalPatients: number;\r\n        totalAppointments: number;\r\n        totalDepartments: number;\r\n        appointmentsToday: number;\r\n        newPatientsThisMonth: number;\r\n    };\r\n}\r\n\r\nexport const ReportsManagement = ({ stats }: ReportsManagementProps) => {\r\n    const [selectedReport, setSelectedReport] = useState('overview');\r\n    const [dateRange, setDateRange] = useState('month');\r\n\r\n    const reports = [\r\n        {\r\n            id: 'overview',\r\n            title: 'Tổng quan',\r\n            description: '<PERSON>áo cáo tổng quan về hoạt động của bệnh viện',\r\n            icon: BarChart3,\r\n            color: 'bg-blue-500'\r\n        },\r\n        {\r\n            id: 'appointments',\r\n            title: 'Lịch hẹn',\r\n            description: 'Thống kê lịch hẹn theo thời gian',\r\n            icon: Calendar,\r\n            color: 'bg-green-500'\r\n        },\r\n        {\r\n            id: 'doctors',\r\n            title: '<PERSON><PERSON><PERSON> sĩ',\r\n            description: '<PERSON><PERSON><PERSON> c<PERSON>o hiệu suất và lịch làm việc của bác sĩ',\r\n            icon: UserCheck,\r\n            color: 'bg-purple-500'\r\n        },\r\n        {\r\n            id: 'patients',\r\n            title: 'Bệnh nhân',\r\n            description: 'Thống kê bệnh nhân và phân tích dân số',\r\n            icon: Users,\r\n            color: 'bg-orange-500'\r\n        },\r\n        {\r\n            id: 'departments',\r\n            title: 'Khoa/Phòng ban',\r\n            description: 'Báo cáo hoạt động của các khoa',\r\n            icon: Building2,\r\n            color: 'bg-red-500'\r\n        },\r\n        {\r\n            id: 'financial',\r\n            title: 'Tài chính',\r\n            description: 'Báo cáo doanh thu và chi phí',\r\n            icon: TrendingUp,\r\n            color: 'bg-indigo-500'\r\n        }\r\n    ];\r\n\r\n    const dateRanges = [\r\n        { value: 'week', label: 'Tuần này' },\r\n        { value: 'month', label: 'Tháng này' },\r\n        { value: 'quarter', label: 'Quý này' },\r\n        { value: 'year', label: 'Năm nay' }\r\n    ];\r\n\r\n    const mockChartData = {\r\n        appointments: [\r\n            { month: 'T1', count: 120 },\r\n            { month: 'T2', count: 150 },\r\n            { month: 'T3', count: 180 },\r\n            { month: 'T4', count: 200 },\r\n            { month: 'T5', count: 220 },\r\n            { month: 'T6', count: 250 }\r\n        ],\r\n        patients: [\r\n            { age: '0-12', count: 45 },\r\n            { age: '13-19', count: 78 },\r\n            { age: '20-59', count: 234 },\r\n            { age: '60+', count: 89 }\r\n        ],\r\n        departments: [\r\n            { name: 'Khoa Nội', appointments: 156, patients: 89 },\r\n            { name: 'Khoa Ngoại', appointments: 134, patients: 67 },\r\n            { name: 'Khoa Nhi', appointments: 98, patients: 45 },\r\n            { name: 'Khoa Tim mạch', appointments: 87, patients: 34 },\r\n            { name: 'Khoa Thần kinh', appointments: 76, patients: 28 }\r\n        ]\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Header */}\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Báo cáo & Thống kê</h2>\r\n                <div className=\"flex items-center space-x-4\">\r\n                    <select\r\n                        value={dateRange}\r\n                        onChange={(e) => setDateRange(e.target.value)}\r\n                        className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    >\r\n                        {dateRanges.map((range) => (\r\n                            <option key={range.value} value={range.value}>\r\n                                {range.label}\r\n                            </option>\r\n                        ))}\r\n                    </select>\r\n                    <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\">\r\n                        <Download className=\"w-4 h-4\" />\r\n                        <span>Xuất báo cáo</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Report Types */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                {reports.map((report) => (\r\n                    <div\r\n                        key={report.id}\r\n                        onClick={() => setSelectedReport(report.id)}\r\n                        className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all hover:shadow-md ${\r\n                            selectedReport === report.id ? 'ring-2 ring-blue-500' : ''\r\n                        }`}\r\n                    >\r\n                        <div className=\"flex items-center space-x-4\">\r\n                            <div className={`p-3 rounded-full ${report.color}`}>\r\n                                <report.icon className=\"w-6 h-6 text-white\" />\r\n                            </div>\r\n                            <div>\r\n                                <h3 className=\"text-lg font-semibold text-gray-900\">{report.title}</h3>\r\n                                <p className=\"text-sm text-gray-500\">{report.description}</p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n\r\n            {/* Report Content */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border p-6\">\r\n                {selectedReport === 'overview' && (\r\n                    <div className=\"space-y-6\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900\">Tổng quan hoạt động</h3>\r\n                        \r\n                        {/* Key Metrics */}\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n                            <div className=\"bg-blue-50 rounded-lg p-4\">\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <div>\r\n                                        <p className=\"text-sm font-medium text-blue-600\">Tổng bác sĩ</p>\r\n                                        <p className=\"text-2xl font-bold text-blue-900\">{stats.totalDoctors}</p>\r\n                                    </div>\r\n                                    <UserCheck className=\"w-8 h-8 text-blue-600\" />\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"bg-green-50 rounded-lg p-4\">\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <div>\r\n                                        <p className=\"text-sm font-medium text-green-600\">Tổng bệnh nhân</p>\r\n                                        <p className=\"text-2xl font-bold text-green-900\">{stats.totalPatients}</p>\r\n                                    </div>\r\n                                    <Users className=\"w-8 h-8 text-green-600\" />\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"bg-purple-50 rounded-lg p-4\">\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <div>\r\n                                        <p className=\"text-sm font-medium text-purple-600\">Lịch hẹn hôm nay</p>\r\n                                        <p className=\"text-2xl font-bold text-purple-900\">{stats.appointmentsToday}</p>\r\n                                    </div>\r\n                                    <Calendar className=\"w-8 h-8 text-purple-600\" />\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"bg-orange-50 rounded-lg p-4\">\r\n                                <div className=\"flex items-center justify-between\">\r\n                                    <div>\r\n                                        <p className=\"text-sm font-medium text-orange-600\">Khoa/Phòng ban</p>\r\n                                        <p className=\"text-2xl font-bold text-orange-900\">{stats.totalDepartments}</p>\r\n                                    </div>\r\n                                    <Building2 className=\"w-8 h-8 text-orange-600\" />\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Chart Placeholder */}\r\n                        <div className=\"bg-gray-50 rounded-lg p-8 text-center\">\r\n                            <BarChart3 className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n                            <p className=\"text-gray-500\">Biểu đồ thống kê sẽ được hiển thị ở đây</p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {selectedReport === 'appointments' && (\r\n                    <div className=\"space-y-6\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900\">Thống kê lịch hẹn</h3>\r\n                        \r\n                        {/* Appointment Trends */}\r\n                        <div className=\"bg-gray-50 rounded-lg p-6\">\r\n                            <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Xu hướng lịch hẹn theo tháng</h4>\r\n                            <div className=\"space-y-2\">\r\n                                {mockChartData.appointments.map((item, index) => (\r\n                                    <div key={index} className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">{item.month}</span>\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            <div className=\"w-32 bg-gray-200 rounded-full h-2\">\r\n                                                <div \r\n                                                    className=\"bg-blue-600 h-2 rounded-full\" \r\n                                                    style={{ width: `${(item.count / 250) * 100}%` }}\r\n                                                ></div>\r\n                                            </div>\r\n                                            <span className=\"text-sm font-medium text-gray-900\">{item.count}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {selectedReport === 'patients' && (\r\n                    <div className=\"space-y-6\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900\">Thống kê bệnh nhân</h3>\r\n                        \r\n                        {/* Patient Demographics */}\r\n                        <div className=\"bg-gray-50 rounded-lg p-6\">\r\n                            <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Phân bố độ tuổi</h4>\r\n                            <div className=\"space-y-3\">\r\n                                {mockChartData.patients.map((item, index) => (\r\n                                    <div key={index} className=\"flex items-center justify-between\">\r\n                                        <span className=\"text-sm text-gray-600\">{item.age} tuổi</span>\r\n                                        <div className=\"flex items-center space-x-2\">\r\n                                            <div className=\"w-32 bg-gray-200 rounded-full h-2\">\r\n                                                <div \r\n                                                    className=\"bg-green-600 h-2 rounded-full\" \r\n                                                    style={{ width: `${(item.count / 234) * 100}%` }}\r\n                                                ></div>\r\n                                            </div>\r\n                                            <span className=\"text-sm font-medium text-gray-900\">{item.count}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {selectedReport === 'departments' && (\r\n                    <div className=\"space-y-6\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900\">Báo cáo khoa/phòng ban</h3>\r\n                        \r\n                        {/* Department Performance */}\r\n                        <div className=\"bg-gray-50 rounded-lg p-6\">\r\n                            <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Hiệu suất các khoa</h4>\r\n                            <div className=\"space-y-4\">\r\n                                {mockChartData.departments.map((dept, index) => (\r\n                                    <div key={index} className=\"bg-white rounded-lg p-4\">\r\n                                        <div className=\"flex items-center justify-between mb-2\">\r\n                                            <h5 className=\"font-medium text-gray-900\">{dept.name}</h5>\r\n                                            <span className=\"text-sm text-gray-500\">\r\n                                                {dept.appointments} lịch hẹn\r\n                                            </span>\r\n                                        </div>\r\n                                        <div className=\"flex items-center justify-between text-sm\">\r\n                                            <span className=\"text-gray-600\">Bệnh nhân: {dept.patients}</span>\r\n                                            <span className=\"text-gray-600\">\r\n                                                Tỷ lệ: {((dept.patients / dept.appointments) * 100).toFixed(1)}%\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {selectedReport === 'doctors' && (\r\n                    <div className=\"space-y-6\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900\">Báo cáo bác sĩ</h3>\r\n                        <div className=\"bg-gray-50 rounded-lg p-8 text-center\">\r\n                            <UserCheck className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n                            <p className=\"text-gray-500\">Thống kê hiệu suất bác sĩ sẽ được hiển thị ở đây</p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n\r\n                {selectedReport === 'financial' && (\r\n                    <div className=\"space-y-6\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900\">Báo cáo tài chính</h3>\r\n                        <div className=\"bg-gray-50 rounded-lg p-8 text-center\">\r\n                            <TrendingUp className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n                            <p className=\"text-gray-500\">Báo cáo tài chính sẽ được hiển thị ở đây</p>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAFA;;;AAeO,MAAM,oBAAoB,CAAC,EAAE,KAAK,EAA0B;;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,UAAU;QACZ;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;QACX;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;QACX;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,mNAAA,CAAA,YAAS;YAC<PERSON>,OAAO;QACX;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACX;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;QACX;QACA;YACI,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;QACX;KACH;IAED,MAAM,aAAa;QACf;YAAE,OAAO;YAAQ,OAAO;QAAW;QACnC;YAAE,OAAO;YAAS,OAAO;QAAY;QACrC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAQ,OAAO;QAAU;KACrC;IAED,MAAM,gBAAgB;QAClB,cAAc;YACV;gBAAE,OAAO;gBAAM,OAAO;YAAI;YAC1B;gBAAE,OAAO;gBAAM,OAAO;YAAI;YAC1B;gBAAE,OAAO;gBAAM,OAAO;YAAI;YAC1B;gBAAE,OAAO;gBAAM,OAAO;YAAI;YAC1B;gBAAE,OAAO;gBAAM,OAAO;YAAI;YAC1B;gBAAE,OAAO;gBAAM,OAAO;YAAI;SAC7B;QACD,UAAU;YACN;gBAAE,KAAK;gBAAQ,OAAO;YAAG;YACzB;gBAAE,KAAK;gBAAS,OAAO;YAAG;YAC1B;gBAAE,KAAK;gBAAS,OAAO;YAAI;YAC3B;gBAAE,KAAK;gBAAO,OAAO;YAAG;SAC3B;QACD,aAAa;YACT;gBAAE,MAAM;gBAAY,cAAc;gBAAK,UAAU;YAAG;YACpD;gBAAE,MAAM;gBAAc,cAAc;gBAAK,UAAU;YAAG;YACtD;gBAAE,MAAM;gBAAY,cAAc;gBAAI,UAAU;YAAG;YACnD;gBAAE,MAAM;gBAAiB,cAAc;gBAAI,UAAU;YAAG;YACxD;gBAAE,MAAM;gBAAkB,cAAc;gBAAI,UAAU;YAAG;SAC5D;IACL;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;0CAET,WAAW,GAAG,CAAC,CAAC,sBACb,6LAAC;wCAAyB,OAAO,MAAM,KAAK;kDACvC,MAAM,KAAK;uCADH,MAAM,KAAK;;;;;;;;;;0CAKhC,6LAAC;gCAAO,WAAU;;kDACd,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC;gBAAI,WAAU;0BACV,QAAQ,GAAG,CAAC,CAAC,uBACV,6LAAC;wBAEG,SAAS,IAAM,kBAAkB,OAAO,EAAE;wBAC1C,WAAW,CAAC,uFAAuF,EAC/F,mBAAmB,OAAO,EAAE,GAAG,yBAAyB,IAC1D;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,OAAO,KAAK,EAAE;8CAC9C,cAAA,6LAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;;sDACG,6LAAC;4CAAG,WAAU;sDAAuC,OAAO,KAAK;;;;;;sDACjE,6LAAC;4CAAE,WAAU;sDAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;uBAZ3D,OAAO,EAAE;;;;;;;;;;0BAoB1B,6LAAC;gBAAI,WAAU;;oBACV,mBAAmB,4BAChB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAoC,MAAM,YAAY;;;;;;;;;;;;8DAEvE,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG7B,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAAqC,MAAM,aAAa;;;;;;;;;;;;8DAEzE,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAE,WAAU;sEAAsC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAsC,MAAM,iBAAiB;;;;;;;;;;;;8DAE9E,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG5B,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;;sEACG,6LAAC;4DAAE,WAAU;sEAAsC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAAsC,MAAM,gBAAgB;;;;;;;;;;;;8DAE7E,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;oBAKxC,mBAAmB,gCAChB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACV,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnC,6LAAC;gDAAgB,WAAU;;kEACvB,6LAAC;wDAAK,WAAU;kEAAyB,KAAK,KAAK;;;;;;kEACnD,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEACG,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,AAAC,KAAK,KAAK,GAAG,MAAO,IAAI,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGvD,6LAAC;gEAAK,WAAU;0EAAqC,KAAK,KAAK;;;;;;;;;;;;;+CAT7D;;;;;;;;;;;;;;;;;;;;;;oBAkB7B,mBAAmB,4BAChB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACV,cAAc,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;gDAAgB,WAAU;;kEACvB,6LAAC;wDAAK,WAAU;;4DAAyB,KAAK,GAAG;4DAAC;;;;;;;kEAClD,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEACG,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,AAAC,KAAK,KAAK,GAAG,MAAO,IAAI,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGvD,6LAAC;gEAAK,WAAU;0EAAqC,KAAK,KAAK;;;;;;;;;;;;;+CAT7D;;;;;;;;;;;;;;;;;;;;;;oBAkB7B,mBAAmB,+BAChB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACV,cAAc,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,6LAAC;gDAAgB,WAAU;;kEACvB,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAG,WAAU;0EAA6B,KAAK,IAAI;;;;;;0EACpD,6LAAC;gEAAK,WAAU;;oEACX,KAAK,YAAY;oEAAC;;;;;;;;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAK,WAAU;;oEAAgB;oEAAY,KAAK,QAAQ;;;;;;;0EACzD,6LAAC;gEAAK,WAAU;;oEAAgB;oEACpB,CAAC,AAAC,KAAK,QAAQ,GAAG,KAAK,YAAY,GAAI,GAAG,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;+CAVjE;;;;;;;;;;;;;;;;;;;;;;oBAoB7B,mBAAmB,2BAChB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;oBAKxC,mBAAmB,6BAChB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD;GA9Ra;KAAA", "debugId": null}}, {"offset": {"line": 5754, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/users/UsersManagement.tsx"], "sourcesContent": ["'use client'\r\nimport { useState } from 'react';\r\nimport { Plus, Search, Edit, Trash2, Shield, User, UserCheck, Building2 } from 'lucide-react';\r\n\r\ninterface SystemUser {\r\n    id: string;\r\n    username: string;\r\n    email: string;\r\n    fullName: string;\r\n    role: 'admin' | 'manager' | 'doctor' | 'receptionist' | 'patient';\r\n    department?: string;\r\n    status: 'active' | 'inactive';\r\n    lastLogin?: string;\r\n    createdAt: string;\r\n}\r\n\r\ninterface UsersManagementProps {\r\n    users: SystemUser[];\r\n}\r\n\r\nexport const UsersManagement = ({ users }: UsersManagementProps) => {\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [roleFilter, setRoleFilter] = useState('all');\r\n    const [statusFilter, setStatusFilter] = useState('all');\r\n    const [showAddModal, setShowAddModal] = useState(false);\r\n    const [selectedUser, setSelectedUser] = useState<SystemUser | null>(null);\r\n\r\n    const roleOptions = [\r\n        { value: 'all', label: '<PERSON>ất cả vai trò' },\r\n        { value: 'admin', label: 'Quản trị viên' },\r\n        { value: 'manager', label: 'Quản lý' },\r\n        { value: 'doctor', label: 'Bác sĩ' },\r\n        { value: 'receptionist', label: 'Lễ tân' },\r\n        { value: 'patient', label: 'Bệnh nhân' }\r\n    ];\r\n\r\n    const statusOptions = [\r\n        { value: 'all', label: 'Tất cả trạng thái' },\r\n        { value: 'active', label: 'Hoạt động' },\r\n        { value: 'inactive', label: 'Không hoạt động' }\r\n    ];\r\n\r\n    const filteredUsers = users.filter(user => {\r\n        const matchesSearch = \r\n            user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            user.fullName.toLowerCase().includes(searchTerm.toLowerCase());\r\n        \r\n        const matchesRole = roleFilter === 'all' || user.role === roleFilter;\r\n        const matchesStatus = statusFilter === 'all' || user.status === statusFilter;\r\n\r\n        return matchesSearch && matchesRole && matchesStatus;\r\n    });\r\n\r\n    const getRoleColor = (role: string) => {\r\n        switch (role) {\r\n            case 'admin':\r\n                return 'bg-red-100 text-red-800';\r\n            case 'manager':\r\n                return 'bg-purple-100 text-purple-800';\r\n            case 'doctor':\r\n                return 'bg-blue-100 text-blue-800';\r\n            case 'receptionist':\r\n                return 'bg-green-100 text-green-800';\r\n            case 'patient':\r\n                return 'bg-gray-100 text-gray-800';\r\n            default:\r\n                return 'bg-gray-100 text-gray-800';\r\n        }\r\n    };\r\n\r\n    const getRoleText = (role: string) => {\r\n        switch (role) {\r\n            case 'admin':\r\n                return 'Quản trị viên';\r\n            case 'manager':\r\n                return 'Quản lý';\r\n            case 'doctor':\r\n                return 'Bác sĩ';\r\n            case 'receptionist':\r\n                return 'Lễ tân';\r\n            case 'patient':\r\n                return 'Bệnh nhân';\r\n            default:\r\n                return 'Không xác định';\r\n        }\r\n    };\r\n\r\n    const getRoleIcon = (role: string) => {\r\n        switch (role) {\r\n            case 'admin':\r\n                return Shield;\r\n            case 'manager':\r\n                return UserCheck;\r\n            case 'doctor':\r\n                return UserCheck;\r\n            case 'receptionist':\r\n                return Building2;\r\n            case 'patient':\r\n                return User;\r\n            default:\r\n                return User;\r\n        }\r\n    };\r\n\r\n    const getStatusColor = (status: string) => {\r\n        return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';\r\n    };\r\n\r\n    const getStatusText = (status: string) => {\r\n        return status === 'active' ? 'Hoạt động' : 'Không hoạt động';\r\n    };\r\n\r\n    const formatDate = (dateString: string) => {\r\n        const date = new Date(dateString);\r\n        return date.toLocaleDateString('vi-VN');\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            {/* Header */}\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Quản lý Người dùng</h2>\r\n                <button\r\n                    onClick={() => setShowAddModal(true)}\r\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\r\n                >\r\n                    <Plus className=\"w-4 h-4\" />\r\n                    <span>Thêm người dùng</span>\r\n                </button>\r\n            </div>\r\n\r\n            {/* Filters */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                    <div className=\"relative\">\r\n                        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                        <input\r\n                            type=\"text\"\r\n                            placeholder=\"Tìm kiếm người dùng...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        />\r\n                    </div>\r\n                    <div>\r\n                        <select\r\n                            value={roleFilter}\r\n                            onChange={(e) => setRoleFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            {roleOptions.map((option) => (\r\n                                <option key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n                    <div>\r\n                        <select\r\n                            value={statusFilter}\r\n                            onChange={(e) => setStatusFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                        >\r\n                            {statusOptions.map((option) => (\r\n                                <option key={option.value} value={option.value}>\r\n                                    {option.label}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n                    <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\">\r\n                        Xuất danh sách\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Users Table */}\r\n            <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\r\n                <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                        <thead className=\"bg-gray-50\">\r\n                            <tr>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Người dùng\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Vai trò\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Khoa/Phòng ban\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Trạng thái\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Đăng nhập cuối\r\n                                </th>\r\n                                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                    Thao tác\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {filteredUsers.map((user) => {\r\n                                const RoleIcon = getRoleIcon(user.role);\r\n                                return (\r\n                                    <tr key={user.id} className=\"hover:bg-gray-50\">\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <div className=\"flex items-center\">\r\n                                                <div className=\"h-10 w-10 flex-shrink-0\">\r\n                                                    <div className=\"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center\">\r\n                                                        <RoleIcon className=\"w-5 h-5 text-gray-500\" />\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"ml-4\">\r\n                                                    <div className=\"text-sm font-medium text-gray-900\">{user.fullName}</div>\r\n                                                    <div className=\"text-sm text-gray-500\">{user.email}</div>\r\n                                                    <div className=\"text-sm text-gray-500\">@{user.username}</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(user.role)}`}>\r\n                                                {getRoleText(user.role)}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <div className=\"text-sm text-gray-900\">\r\n                                                {user.department || 'Không có'}\r\n                                            </div>\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(user.status)}`}>\r\n                                                {getStatusText(user.status)}\r\n                                            </span>\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                            <div className=\"text-sm text-gray-900\">\r\n                                                {user.lastLogin ? formatDate(user.lastLogin) : 'Chưa đăng nhập'}\r\n                                            </div>\r\n                                        </td>\r\n                                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                            <div className=\"flex space-x-2\">\r\n                                                <button\r\n                                                    onClick={() => setSelectedUser(user)}\r\n                                                    className=\"text-blue-600 hover:text-blue-900\"\r\n                                                >\r\n                                                    Xem\r\n                                                </button>\r\n                                                <button\r\n                                                    onClick={() => setSelectedUser(user)}\r\n                                                    className=\"text-green-600 hover:text-green-900\"\r\n                                                >\r\n                                                    <Edit className=\"w-4 h-4\" />\r\n                                                </button>\r\n                                                <button\r\n                                                    onClick={() => {\r\n                                                        if (confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {\r\n                                                            // Handle delete\r\n                                                        }\r\n                                                    }}\r\n                                                    className=\"text-red-600 hover:text-red-900\"\r\n                                                >\r\n                                                    <Trash2 className=\"w-4 h-4\" />\r\n                                                </button>\r\n                                            </div>\r\n                                        </td>\r\n                                    </tr>\r\n                                );\r\n                            })}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Statistics */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-blue-100 rounded-full\">\r\n                            <User className=\"w-6 h-6 text-blue-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Tổng người dùng</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">{users.length}</p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-green-100 rounded-full\">\r\n                            <Shield className=\"w-6 h-6 text-green-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Quản trị viên</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {users.filter(u => u.role === 'admin').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-purple-100 rounded-full\">\r\n                            <UserCheck className=\"w-6 h-6 text-purple-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Bác sĩ</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {users.filter(u => u.role === 'doctor').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n                    <div className=\"flex items-center\">\r\n                        <div className=\"p-2 bg-orange-100 rounded-full\">\r\n                            <Building2 className=\"w-6 h-6 text-orange-600\" />\r\n                        </div>\r\n                        <div className=\"ml-4\">\r\n                            <p className=\"text-sm font-medium text-gray-600\">Lễ tân</p>\r\n                            <p className=\"text-2xl font-bold text-gray-900\">\r\n                                {users.filter(u => u.role === 'receptionist').length}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Add/Edit User Modal */}\r\n            {showAddModal && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n                    <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n                            {selectedUser ? 'Chỉnh sửa Người dùng' : 'Thêm Người dùng mới'}\r\n                        </h3>\r\n                        <form className=\"space-y-4\">\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Tên đầy đủ</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    defaultValue={selectedUser?.fullName || ''}\r\n                                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Tên đăng nhập</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    defaultValue={selectedUser?.username || ''}\r\n                                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Email</label>\r\n                                <input\r\n                                    type=\"email\"\r\n                                    defaultValue={selectedUser?.email || ''}\r\n                                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Vai trò</label>\r\n                                <select \r\n                                    defaultValue={selectedUser?.role || 'patient'}\r\n                                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                >\r\n                                    {roleOptions.slice(1).map((option) => (\r\n                                        <option key={option.value} value={option.value}>\r\n                                            {option.label}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                            </div>\r\n                            <div>\r\n                                <label className=\"block text-sm font-medium text-gray-700\">Trạng thái</label>\r\n                                <select \r\n                                    defaultValue={selectedUser?.status || 'active'}\r\n                                    className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                >\r\n                                    <option value=\"active\">Hoạt động</option>\r\n                                    <option value=\"inactive\">Không hoạt động</option>\r\n                                </select>\r\n                            </div>\r\n                            <div className=\"flex justify-end space-x-3 pt-4\">\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={() => {\r\n                                        setShowAddModal(false);\r\n                                        setSelectedUser(null);\r\n                                    }}\r\n                                    className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200\"\r\n                                >\r\n                                    Hủy\r\n                                </button>\r\n                                <button\r\n                                    type=\"submit\"\r\n                                    className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\"\r\n                                >\r\n                                    {selectedUser ? 'Cập nhật' : 'Thêm'}\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAFA;;;AAoBO,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAwB;;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAEpE,MAAM,cAAc;QAChB;YAAE,OAAO;YAAO,OAAO;QAAiB;QACxC;YAAE,OAAO;YAAS,OAAO;QAAgB;QACzC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAgB,OAAO;QAAS;QACzC;YAAE,OAAO;YAAW,OAAO;QAAY;KAC1C;IAED,MAAM,gBAAgB;QAClB;YAAE,OAAO;YAAO,OAAO;QAAoB;QAC3C;YAAE,OAAO;YAAU,OAAO;QAAY;QACtC;YAAE,OAAO;YAAY,OAAO;QAAkB;KACjD;IAED,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QAC/B,MAAM,gBACF,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE/D,MAAM,cAAc,eAAe,SAAS,KAAK,IAAI,KAAK;QAC1D,MAAM,gBAAgB,iBAAiB,SAAS,KAAK,MAAM,KAAK;QAEhE,OAAO,iBAAiB,eAAe;IAC3C;IAEA,MAAM,eAAe,CAAC;QAClB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,cAAc,CAAC;QACjB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,cAAc,CAAC;QACjB,OAAQ;YACJ,KAAK;gBACD,OAAO,yMAAA,CAAA,SAAM;YACjB,KAAK;gBACD,OAAO,mNAAA,CAAA,YAAS;YACpB,KAAK;gBACD,OAAO,mNAAA,CAAA,YAAS;YACpB,KAAK;gBACD,OAAO,mNAAA,CAAA,YAAS;YACpB,KAAK;gBACD,OAAO,qMAAA,CAAA,OAAI;YACf;gBACI,OAAO,qMAAA,CAAA,OAAI;QACnB;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,OAAO,WAAW,WAAW,gCAAgC;IACjE;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAO,WAAW,WAAW,cAAc;IAC/C;IAEA,MAAM,aAAa,CAAC;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC;IACnC;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBACG,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACG,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAGlB,6LAAC;sCACG,cAAA,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,uBACd,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDACzC,OAAO,KAAK;uCADJ,OAAO,KAAK;;;;;;;;;;;;;;;sCAMrC,6LAAC;sCACG,cAAA,6LAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;0CAET,cAAc,GAAG,CAAC,CAAC,uBAChB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDACzC,OAAO,KAAK;uCADJ,OAAO,KAAK;;;;;;;;;;;;;;;sCAMrC,6LAAC;4BAAO,WAAU;sCAAkE;;;;;;;;;;;;;;;;;0BAO5F,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAM,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACb,cAAA,6LAAC;;sDACG,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKvG,6LAAC;gCAAM,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC;oCAChB,MAAM,WAAW,YAAY,KAAK,IAAI;oCACtC,qBACI,6LAAC;wCAAiB,WAAU;;0DACxB,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DAAI,WAAU;sEACX,cAAA,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAS,WAAU;;;;;;;;;;;;;;;;sEAG5B,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEAAI,WAAU;8EAAqC,KAAK,QAAQ;;;;;;8EACjE,6LAAC;oEAAI,WAAU;8EAAyB,KAAK,KAAK;;;;;;8EAClD,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAE,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0DAIlE,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,aAAa,KAAK,IAAI,GAAG;8DACjG,YAAY,KAAK,IAAI;;;;;;;;;;;0DAG9B,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACV,KAAK,UAAU,IAAI;;;;;;;;;;;0DAG5B,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,KAAK,MAAM,GAAG;8DACrG,cAAc,KAAK,MAAM;;;;;;;;;;;0DAGlC,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACV,KAAK,SAAS,GAAG,WAAW,KAAK,SAAS,IAAI;;;;;;;;;;;0DAGvD,6LAAC;gDAAG,WAAU;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DACG,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;sEACb;;;;;;sEAGD,6LAAC;4DACG,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;sEAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAEpB,6LAAC;4DACG,SAAS;gEACL,IAAI,QAAQ,8CAA8C;gEACtD,gBAAgB;gEACpB;4DACJ;4DACA,WAAU;sEAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAzDzB,KAAK,EAAE;;;;;gCA+DxB;;;;;;;;;;;;;;;;;;;;;;0BAOhB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK7D,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK9D,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDACR,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQvE,8BACG,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCACT,eAAe,yBAAyB;;;;;;sCAE7C,6LAAC;4BAAK,WAAU;;8CACZ,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,cAAc,cAAc,YAAY;4CACxC,WAAU;;;;;;;;;;;;8CAGlB,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,cAAc,cAAc,YAAY;4CACxC,WAAU;;;;;;;;;;;;8CAGlB,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,MAAK;4CACL,cAAc,cAAc,SAAS;4CACrC,WAAU;;;;;;;;;;;;8CAGlB,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,cAAc,cAAc,QAAQ;4CACpC,WAAU;sDAET,YAAY,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,uBACvB,6LAAC;oDAA0B,OAAO,OAAO,KAAK;8DACzC,OAAO,KAAK;mDADJ,OAAO,KAAK;;;;;;;;;;;;;;;;8CAMrC,6LAAC;;sDACG,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,6LAAC;4CACG,cAAc,cAAc,UAAU;4CACtC,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;;8CAGjC,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CACG,MAAK;4CACL,SAAS;gDACL,gBAAgB;gDAChB,gBAAgB;4CACpB;4CACA,WAAU;sDACb;;;;;;sDAGD,6LAAC;4CACG,MAAK;4CACL,WAAU;sDAET,eAAe,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjE;GArYa;KAAA", "debugId": null}}, {"offset": {"line": 6783, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/%28page%29/%28manager%29/manager/page.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState } from 'react';\r\nimport { StatsCards } from '@/components/manager/layouts/StatsCards';\r\nimport { Dashboard } from '@/components/manager/dashboard/Dashboard';\r\nimport { DoctorsManagement } from '@/components/manager/doctors/DoctorsManagement';\r\nimport { DepartmentsManagement } from '@/components/manager/departments/DepartmentsManagement';\r\nimport { AppointmentsManagement } from '@/components/manager/appointments/AppointmentsManagement';\r\nimport { PatientsManagement } from '@/components/manager/patients/PatientsManagement';\r\nimport { ReportsManagement } from '@/components/manager/reports/ReportsManagement';\r\nimport { UsersManagement } from '@/components/manager/users/UsersManagement';\r\nimport { ManagerStats } from '@/types/manager';\r\nimport { Doctor } from '@/types/doctor';\r\nimport { Appointment } from '@/types/appointment';\r\nimport { Patient } from '@/types/appointment';\r\n\r\nexport default function ManagerPage() {\r\n    const [activeTab, setActiveTab] = useState('dashboard');\r\n\r\n    // Mock data - in real app, this would come from API\r\n    const stats: ManagerStats = {\r\n        totalDoctors: 25,\r\n        totalPatients: 1250,\r\n        totalAppointments: 450,\r\n        totalDepartments: 8,\r\n        appointmentsToday: 45,\r\n        newPatientsThisMonth: 89,\r\n        totalRevenue: 12500000,\r\n        pendingAppointments: 12\r\n    };\r\n\r\n    const mockDoctors: Doctor[] = [\r\n        {\r\n            id: '1',\r\n            name: 'Bác sĩ Nguyễn Văn A',\r\n            department: 'Khoa Nội',\r\n            schedule: {\r\n                weekdays: '8:00 - 17:00',\r\n                saturday: '8:00 - 12:00'\r\n            },\r\n            status: 'available',\r\n            avatar: '/default-avatar.png'\r\n        },\r\n        {\r\n            id: '2',\r\n            name: 'Bác sĩ Trần Thị B',\r\n            department: 'Khoa Ngoại',\r\n            schedule: {\r\n                weekdays: '8:00 - 17:00'\r\n            },\r\n            status: 'busy',\r\n            avatar: '/default-avatar.png'\r\n        }\r\n    ];\r\n\r\n    const mockAppointments: Appointment[] = [\r\n        {\r\n            id: '1',\r\n            patientId: '1',\r\n            patientName: 'Nguyễn Thị D',\r\n            phone: '**********',\r\n            doctorName: 'Bác sĩ Phạm Văn E',\r\n            department: 'Khoa Nội',\r\n            date: '2025-01-15',\r\n            time: '09:00',\r\n            status: 'confirmed',\r\n            symptoms: 'Đau đầu, sốt'\r\n        },\r\n        {\r\n            id: '2',\r\n            patientId: '2',\r\n            patientName: 'Trần Văn F',\r\n            phone: '**********',\r\n            doctorName: 'Bác sĩ Lê Thị G',\r\n            department: 'Khoa Ngoại',\r\n            date: '2025-01-15',\r\n            time: '10:30',\r\n            status: 'pending',\r\n            symptoms: 'Đau bụng'\r\n        }\r\n    ];\r\n\r\n    const mockPatients: Patient[] = [\r\n        {\r\n            id: '1',\r\n            name: 'Nguyễn Thị D',\r\n            phone: '**********',\r\n            email: '<EMAIL>',\r\n            address: '123 Đường ABC, Quận 1, TP.HCM',\r\n            birthDate: '1990-05-15',\r\n            gender: 'female',\r\n            lastVisit: '2025-01-10'\r\n        },\r\n        {\r\n            id: '2',\r\n            name: 'Trần Văn F',\r\n            phone: '**********',\r\n            email: '<EMAIL>',\r\n            address: '456 Đường XYZ, Quận 2, TP.HCM',\r\n            birthDate: '1985-08-20',\r\n            gender: 'male',\r\n            lastVisit: '2025-01-12'\r\n        }\r\n    ];\r\n\r\n    const renderContent = () => {\r\n        switch (activeTab) {\r\n            case 'dashboard':\r\n                return <Dashboard stats={stats} />;\r\n            case 'doctors':\r\n                return <DoctorsManagement doctors={mockDoctors} />;\r\n            case 'departments':\r\n                return <DepartmentsManagement departments={[]} />;\r\n            case 'appointments':\r\n                return <AppointmentsManagement appointments={mockAppointments} />;\r\n            case 'patients':\r\n                return <PatientsManagement patients={mockPatients} />;\r\n            case 'reports':\r\n                return <ReportsManagement stats={stats} />;\r\n            case 'users':\r\n                return <UsersManagement users={[]} />;\r\n            case 'settings':\r\n                return (\r\n                    <div className=\"bg-white rounded-lg shadow-sm border p-6\">\r\n                        <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Cài đặt hệ thống</h2>\r\n                        <p className=\"text-gray-600\">Trang cài đặt sẽ được phát triển sau.</p>\r\n                    </div>\r\n                );\r\n            default:\r\n                return <Dashboard stats={stats} />;\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            {activeTab === 'dashboard' && <StatsCards stats={stats} />}\r\n            {renderContent()}\r\n        </div>\r\n    );\r\n} "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAee,SAAS;;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oDAAoD;IACpD,MAAM,QAAsB;QACxB,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,sBAAsB;QACtB,cAAc;QACd,qBAAqB;IACzB;IAEA,MAAM,cAAwB;QAC1B;YACI,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,UAAU;gBACN,UAAU;gBACV,UAAU;YACd;YACA,QAAQ;YACR,QAAQ;QACZ;QACA;YACI,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,UAAU;gBACN,UAAU;YACd;YACA,QAAQ;YACR,QAAQ;QACZ;KACH;IAED,MAAM,mBAAkC;QACpC;YACI,IAAI;YACJ,WAAW;YACX,aAAa;YACb,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACd;QACA;YACI,IAAI;YACJ,WAAW;YACX,aAAa;YACb,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACd;KACH;IAED,MAAM,eAA0B;QAC5B;YACI,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,WAAW;YACX,QAAQ;YACR,WAAW;QACf;QACA;YACI,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,WAAW;YACX,QAAQ;YACR,WAAW;QACf;KACH;IAED,MAAM,gBAAgB;QAClB,OAAQ;YACJ,KAAK;gBACD,qBAAO,6LAAC,0JAAA,CAAA,YAAS;oBAAC,OAAO;;;;;;YAC7B,KAAK;gBACD,qBAAO,6LAAC,gKAAA,CAAA,oBAAiB;oBAAC,SAAS;;;;;;YACvC,KAAK;gBACD,qBAAO,6LAAC,wKAAA,CAAA,wBAAqB;oBAAC,aAAa,EAAE;;;;;;YACjD,KAAK;gBACD,qBAAO,6LAAC,0KAAA,CAAA,yBAAsB;oBAAC,cAAc;;;;;;YACjD,KAAK;gBACD,qBAAO,6LAAC,kKAAA,CAAA,qBAAkB;oBAAC,UAAU;;;;;;YACzC,KAAK;gBACD,qBAAO,6LAAC,gKAAA,CAAA,oBAAiB;oBAAC,OAAO;;;;;;YACrC,KAAK;gBACD,qBAAO,6LAAC,4JAAA,CAAA,kBAAe;oBAAC,OAAO,EAAE;;;;;;YACrC,KAAK;gBACD,qBACI,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;YAGzC;gBACI,qBAAO,6LAAC,0JAAA,CAAA,YAAS;oBAAC,OAAO;;;;;;QACjC;IACJ;IAEA,qBACI,6LAAC;;YACI,cAAc,6BAAe,6LAAC,yJAAA,CAAA,aAAU;gBAAC,OAAO;;;;;;YAChD;;;;;;;AAGb;GA3HwB;KAAA", "debugId": null}}]}