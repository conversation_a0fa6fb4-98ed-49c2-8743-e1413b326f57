import { ApiResponse } from "@/types/apiResonse";
import { Doctor<PERSON><PERSON><PERSON>Request, DoctorCreationResponse } from "@/types/doctor";
import { API_URL } from "@/utils/baseUrl";
import { fetchInterceptor } from "@/utils/Interceptor";

export const createDoctor = async (data: DoctorCreationRequest): Promise<ApiResponse<DoctorCreationResponse>> => {
    const response = await fetchInterceptor(`${API_URL}/api/v1/doctors`, {
        method: "POST",
        body: JSON.stringify(data)
    });

    const result: ApiResponse<DoctorCreationResponse> = await response.json();
    return result;
};
